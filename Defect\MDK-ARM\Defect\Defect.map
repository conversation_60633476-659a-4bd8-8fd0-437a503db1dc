Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(STACK) for __initial_sp
    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(.text) for Reset_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32h723xx.o(.text) refers to system_stm32h7xx.o(i.ExitRun0Mode) for ExitRun0Mode
    startup_stm32h723xx.o(.text) refers to system_stm32h7xx.o(i.SystemInit) for SystemInit
    startup_stm32h723xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) for HAL_PWREx_ConfigSupply
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable) for HAL_MPU_Disable
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion) for HAL_MPU_ConfigRegion
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable) for HAL_MPU_Enable
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to main_program.o(i.user_init_program) for user_init_program
    main.o(i.main) refers to main_program.o(i.user_main_program) for user_main_program
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C2_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM4_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART3_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART3_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32h7xx_it.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32h7xx_it.o(i.TIM2_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32h7xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32h7xx_it.o(i.USART2_IRQHandler) refers to esp8266.o(i.uart2_receiver_handle) for uart2_receiver_handle
    stm32h7xx_it.o(i.USART2_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32h7xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32h7xx_it.o(i.USART3_IRQHandler) refers to atgm336h.o(i.atgm336h_usart3_irq_handler) for atgm336h_usart3_irq_handler
    stm32h7xx_it.o(i.USART3_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32h7xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32h7xx_it.o(i.USART6_IRQHandler) refers to v831.o(i.v831_usart6_irq_handler) for v831_usart6_irq_handler
    stm32h7xx_it.o(i.USART6_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32h7xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback) for HAL_HSEM_FreeCallback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for .constdata
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to time_handle.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAError) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to time_handle.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_uart.o(.constdata) for .constdata
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32h7xx_hal_uart_ex.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for .data
    core_json.o(i.JSON_Iterate) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.JSON_Iterate) refers to core_json.o(i.iterate) for iterate
    core_json.o(i.JSON_Iterate) refers to core_json.o(i.getType) for getType
    core_json.o(i.JSON_SearchConst) refers to core_json.o(i.multiSearch) for multiSearch
    core_json.o(i.JSON_SearchConst) refers to core_json.o(i.getType) for getType
    core_json.o(i.JSON_SearchT) refers to core_json.o(i.JSON_SearchConst) for JSON_SearchConst
    core_json.o(i.JSON_Validate) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.JSON_Validate) refers to core_json.o(i.skipAnyScalar) for skipAnyScalar
    core_json.o(i.JSON_Validate) refers to core_json.o(i.skipCollection) for skipCollection
    core_json.o(i.arraySearch) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.arraySearch) refers to core_json.o(i.nextValue) for nextValue
    core_json.o(i.arraySearch) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.iterate) refers to core_json.o(i.nextValue) for nextValue
    core_json.o(i.iterate) refers to core_json.o(i.nextKeyValuePair) for nextKeyValuePair
    core_json.o(i.iterate) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.multiSearch) refers to core_json.o(i.skipDigits) for skipDigits
    core_json.o(i.multiSearch) refers to core_json.o(i.arraySearch) for arraySearch
    core_json.o(i.multiSearch) refers to core_json.o(i.objectSearch) for objectSearch
    core_json.o(i.nextKeyValuePair) refers to core_json.o(i.skipString) for skipString
    core_json.o(i.nextKeyValuePair) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.nextKeyValuePair) refers to core_json.o(i.nextValue) for nextValue
    core_json.o(i.nextValue) refers to core_json.o(i.skipAnyScalar) for skipAnyScalar
    core_json.o(i.nextValue) refers to core_json.o(i.skipCollection) for skipCollection
    core_json.o(i.objectSearch) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.objectSearch) refers to core_json.o(i.nextKeyValuePair) for nextKeyValuePair
    core_json.o(i.objectSearch) refers to core_json.o(i.strnEq) for strnEq
    core_json.o(i.objectSearch) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.skipAnyScalar) refers to core_json.o(i.skipString) for skipString
    core_json.o(i.skipAnyScalar) refers to core_json.o(i.skipLiteral) for skipLiteral
    core_json.o(i.skipAnyScalar) refers to core_json.o(i.skipNumber) for skipNumber
    core_json.o(i.skipCollection) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.skipCollection) refers to core_json.o(i.skipScalars) for skipScalars
    core_json.o(i.skipDigits) refers to core_json.o(i.hexToInt) for hexToInt
    core_json.o(i.skipEscape) refers to core_json.o(i.skipOneHexEscape) for skipOneHexEscape
    core_json.o(i.skipLiteral) refers to core_json.o(i.strnEq) for strnEq
    core_json.o(i.skipNumber) refers to core_json.o(i.skipDigits) for skipDigits
    core_json.o(i.skipObjectScalars) refers to core_json.o(i.skipString) for skipString
    core_json.o(i.skipObjectScalars) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.skipObjectScalars) refers to core_json.o(i.skipAnyScalar) for skipAnyScalar
    core_json.o(i.skipObjectScalars) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.skipOneHexEscape) refers to core_json.o(i.hexToInt) for hexToInt
    core_json.o(i.skipScalars) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.skipScalars) refers to core_json.o(i.skipObjectScalars) for skipObjectScalars
    core_json.o(i.skipScalars) refers to core_json.o(i.skipAnyScalar) for skipAnyScalar
    core_json.o(i.skipScalars) refers to core_json.o(i.skipSpaceAndComma) for skipSpaceAndComma
    core_json.o(i.skipSpaceAndComma) refers to core_json.o(i.skipSpace) for skipSpace
    core_json.o(i.skipString) refers to core_json.o(i.skipEscape) for skipEscape
    dht11.o(i.dht11_check) refers to dht11.o(i.dht11_io_in) for dht11_io_in
    dht11.o(i.dht11_check) refers to delay.o(i.delay_us) for delay_us
    dht11.o(i.dht11_check) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.dht11_init) refers to dht11.o(i.dht11_rst) for dht11_rst
    dht11.o(i.dht11_init) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.dht11_init) refers to dht11.o(i.dht11_check) for dht11_check
    dht11.o(i.dht11_io_in) refers to memseta.o(.text) for __aeabi_memclr4
    dht11.o(i.dht11_io_in) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.dht11_io_out) refers to memseta.o(.text) for __aeabi_memclr4
    dht11.o(i.dht11_io_out) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.dht11_read_bit) refers to delay.o(i.delay_us) for delay_us
    dht11.o(i.dht11_read_bit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.dht11_read_byte) refers to dht11.o(i.dht11_read_bit) for dht11_read_bit
    dht11.o(i.dht11_read_data) refers to dht11.o(i.dht11_rst) for dht11_rst
    dht11.o(i.dht11_read_data) refers to dht11.o(i.dht11_check) for dht11_check
    dht11.o(i.dht11_read_data) refers to dht11.o(i.dht11_read_byte) for dht11_read_byte
    dht11.o(i.dht11_rst) refers to dht11.o(i.dht11_io_out) for dht11_io_out
    dht11.o(i.dht11_rst) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.dht11_rst) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.dht11_rst) refers to delay.o(i.delay_us) for delay_us
    esp8266.o(i.esp8266_config_network) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.esp8266_config_network) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_config_network) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_config_network) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_config_network) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_config_network) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_config_network) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.esp8266_connect_server) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.esp8266_connect_server) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_connect_server) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_connect_server) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_connect_server) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_connect_server) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_connect_server) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_connect_server) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.esp8266_init) refers to printfa.o(i.__0printf) for __2printf
    esp8266.o(i.esp8266_init) refers to oled.o(i.OLED_printf) for OLED_printf
    esp8266.o(i.esp8266_init) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_reset) for esp8266_reset
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_config_network) for esp8266_config_network
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_connect_server) for esp8266_connect_server
    esp8266.o(i.esp8266_init) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_init) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_receive_msg) refers to _scanf_int.o(.text) for _scanf_int
    esp8266.o(i.esp8266_receive_msg) refers to _scanf_str.o(.text) for _scanf_string
    esp8266.o(i.esp8266_receive_msg) refers to memseta.o(.text) for __aeabi_memclr4
    esp8266.o(i.esp8266_receive_msg) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_receive_msg) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_receive_msg) refers to __0sscanf.o(.text) for __0sscanf
    esp8266.o(i.esp8266_receive_msg) refers to printfa.o(i.__0printf) for __2printf
    esp8266.o(i.esp8266_receive_msg) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_receive_msg) refers to esp8266.o(i.parse_json_msg) for parse_json_msg
    esp8266.o(i.esp8266_receive_msg) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_receive_msg) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_receive_msg) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.esp8266_receive_msg) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_reset) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.esp8266_reset) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_reset) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_reset) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_reset) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_reset) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_reset) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.esp8266_send_cmd) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.esp8266_send_cmd) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_send_cmd) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_send_cmd) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_send_cmd) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_send_cmd) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_send_cmd) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.esp8266_send_msg) refers to printfa.o(i.__0sprintf) for __2sprintf
    esp8266.o(i.esp8266_send_msg) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_send_msg) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.esp8266_send_msg) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.esp8266_send_msg) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_send_msg) refers to printfa.o(i.__0printf) for __2printf
    esp8266.o(i.esp8266_send_msg) refers to esp8266.o(i.esp8266_init) for esp8266_init
    esp8266.o(i.esp8266_send_msg) refers to esp8266.o(i.uart2_receiver_clear) for uart2_receiver_clear
    esp8266.o(i.esp8266_send_msg) refers to main_program.o(.data) for travel_distance
    esp8266.o(i.esp8266_send_msg) refers to atgm336h.o(.bss) for g_LatAndLongData
    esp8266.o(i.esp8266_send_msg) refers to v831.o(.bss) for g_V831DetectionData
    esp8266.o(i.esp8266_send_msg) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_send_msg) refers to usart.o(.bss) for huart2
    esp8266.o(i.esp8266_send_msg) refers to esp8266.o(.data) for .data
    esp8266.o(i.esp8266_send_msg) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.parse_json_msg) refers to core_json.o(i.JSON_Validate) for JSON_Validate
    esp8266.o(i.parse_json_msg) refers to core_json.o(i.JSON_SearchT) for JSON_SearchT
    esp8266.o(i.parse_json_msg) refers to printfa.o(i.__0printf) for __2printf
    esp8266.o(i.parse_json_msg) refers to atoi.o(.text) for atoi
    esp8266.o(i.parse_json_msg) refers to main_program.o(.data) for led_status
    esp8266.o(i.uart2_receiver_clear) refers to memseta.o(.text) for __aeabi_memclr
    esp8266.o(i.uart2_receiver_clear) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.uart2_receiver_clear) refers to esp8266.o(.data) for .data
    esp8266.o(i.uart2_receiver_handle) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    esp8266.o(i.uart2_receiver_handle) refers to usart.o(.bss) for huart2
    esp8266.o(i.uart2_receiver_handle) refers to esp8266.o(.data) for .data
    esp8266.o(i.uart2_receiver_handle) refers to esp8266.o(.bss) for .bss
    led.o(i.set_led) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main_program.o(i.user_init_program) refers to delay.o(i.delay_init) for delay_init
    main_program.o(i.user_init_program) refers to oled.o(i.OLED_init) for OLED_init
    main_program.o(i.user_init_program) refers to atgm336h.o(i.atgm336h_init) for atgm336h_init
    main_program.o(i.user_init_program) refers to v831.o(i.v831_init) for v831_init
    main_program.o(i.user_init_program) refers to motor.o(i.Motor_Init) for Motor_Init
    main_program.o(i.user_init_program) refers to servo.o(i.SERVO_Init) for SERVO_Init
    main_program.o(i.user_init_program) refers to motor.o(i.Encoder_Init) for Encoder_Init
    main_program.o(i.user_init_program) refers to vl53l0x_i2c_dev.o(i.VL53L0X_Init) for VL53L0X_Init
    main_program.o(i.user_init_program) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main_program.o(i.user_init_program) refers to printfa.o(i.__0printf) for __2printf
    main_program.o(i.user_init_program) refers to oled.o(i.OLED_printf) for OLED_printf
    main_program.o(i.user_init_program) refers to dht11.o(i.dht11_init) for dht11_init
    main_program.o(i.user_init_program) refers to oled.o(i.OLED_operate_gram) for OLED_operate_gram
    main_program.o(i.user_init_program) refers to time_handle.o(i.time_slot_start) for time_slot_start
    main_program.o(i.user_init_program) refers to servo.o(i.SERVO_Release) for SERVO_Release
    main_program.o(i.user_init_program) refers to motor.o(i.Motor_Control) for Motor_Control
    main_program.o(i.user_init_program) refers to tim.o(.bss) for htim4
    main_program.o(i.user_main_program) refers to esp8266.o(i.esp8266_receive_msg) for esp8266_receive_msg
    main_program.o(i.user_main_program) refers to dht11.o(i.dht11_read_data) for dht11_read_data
    main_program.o(i.user_main_program) refers to led.o(i.set_led) for set_led
    main_program.o(i.user_main_program) refers to atgm336h.o(i.parseGpsBuffer) for parseGpsBuffer
    main_program.o(i.user_main_program) refers to atgm336h.o(i.printGpsBuffer) for printGpsBuffer
    main_program.o(i.user_main_program) refers to v831.o(i.parseV831Buffer) for parseV831Buffer
    main_program.o(i.user_main_program) refers to v831.o(i.printV831Data) for printV831Data
    main_program.o(i.user_main_program) refers to vl53l0x_i2c_dev.o(i.VL53L0X_GetValue) for VL53L0X_GetValue
    main_program.o(i.user_main_program) refers to printfa.o(i.__0printf) for __2printf
    main_program.o(i.user_main_program) refers to servo.o(i.SERVO_Clamp) for SERVO_Clamp
    main_program.o(i.user_main_program) refers to motor.o(i.Motor_Control) for Motor_Control
    main_program.o(i.user_main_program) refers to motor.o(i.Encoder_Update) for Encoder_Update
    main_program.o(i.user_main_program) refers to motor.o(i.Encoder_GetDistanceMM) for Encoder_GetDistanceMM
    main_program.o(i.user_main_program) refers to oled.o(i.OLED_printf) for OLED_printf
    main_program.o(i.user_main_program) refers to time_handle.o(.data) for index_dht11
    main_program.o(i.user_main_program) refers to main_program.o(.data) for .data
    main_program.o(i.user_main_program) refers to atgm336h.o(.bss) for g_LatAndLongData
    main_program.o(i.user_main_program) refers to v831.o(.bss) for g_V831DetectionData
    oled.o(i.OLED_LOGO) refers to oled.o(i.OLED_operate_gram) for OLED_operate_gram
    oled.o(i.OLED_LOGO) refers to oled.o(i.OLED_draw_point) for OLED_draw_point
    oled.o(i.OLED_LOGO) refers to oled.o(i.OLED_refresh_gram) for OLED_refresh_gram
    oled.o(i.OLED_LOGO) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_display_off) refers to oled.o(i.oled_write_byte) for oled_write_byte
    oled.o(i.OLED_display_on) refers to oled.o(i.oled_write_byte) for oled_write_byte
    oled.o(i.OLED_draw_line) refers to oled.o(i.OLED_draw_point) for OLED_draw_point
    oled.o(i.OLED_draw_point) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_init) refers to oled.o(i.oled_write_byte) for oled_write_byte
    oled.o(i.OLED_operate_gram) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(i.OLED_printf) refers to oled.o(i.OLED_show_string) for OLED_show_string
    oled.o(i.OLED_printf) refers to oled.o(.data) for .data
    oled.o(i.OLED_printf) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_refresh_gram) refers to oled.o(i.OLED_set_pos) for OLED_set_pos
    oled.o(i.OLED_refresh_gram) refers to oled.o(i.oled_write_byte) for oled_write_byte
    oled.o(i.OLED_refresh_gram) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_set_pos) refers to oled.o(i.oled_write_byte) for oled_write_byte
    oled.o(i.OLED_show_char) refers to oled.o(i.OLED_draw_point) for OLED_draw_point
    oled.o(i.OLED_show_char) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_show_string) refers to oled.o(i.OLED_show_char) for OLED_show_char
    oled.o(i.OLED_show_string) refers to oled.o(i.OLED_refresh_gram) for OLED_refresh_gram
    oled.o(i.oled_write_byte) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.oled_write_byte) refers to oled.o(.data) for .data
    oled.o(i.oled_write_byte) refers to i2c.o(.bss) for hi2c1
    time_handle.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    time_handle.o(i.HAL_TIM_PeriodElapsedCallback) refers to time_handle.o(.data) for .data
    time_handle.o(i.time_slot_start) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    time_handle.o(i.time_slot_start) refers to tim.o(.bss) for htim2
    uart_printf.o(i.fputc) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_printf.o(i.fputc) refers to usart.o(.bss) for huart1
    st_i2c.o(i.IIC_ReadOneByte) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    st_i2c.o(i.IIC_ReadOneByte) refers to i2c.o(.bss) for hi2c2
    st_i2c.o(i.ST_IICreadByte) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    st_i2c.o(i.ST_IICreadBytes) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    st_i2c.o(i.ST_IICreadBytes) refers to i2c.o(.bss) for hi2c2
    st_i2c.o(i.ST_IICwriteBit) refers to st_i2c.o(i.ST_IICreadByte) for ST_IICreadByte
    st_i2c.o(i.ST_IICwriteBit) refers to st_i2c.o(i.ST_IICwriteByte) for ST_IICwriteByte
    st_i2c.o(i.ST_IICwriteByte) refers to st_i2c.o(i.ST_IICwriteBytes) for ST_IICwriteBytes
    st_i2c.o(i.ST_IICwriteBytes) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    st_i2c.o(i.ST_IICwriteBytes) refers to i2c.o(.bss) for hi2c2
    atgm336h.o(i.atgm336h_init) refers to atgm336h.o(i.clrStruct) for clrStruct
    atgm336h.o(i.atgm336h_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    atgm336h.o(i.atgm336h_init) refers to memseta.o(.text) for __aeabi_memclr4
    atgm336h.o(i.atgm336h_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    atgm336h.o(i.atgm336h_init) refers to main.o(i.Error_Handler) for Error_Handler
    atgm336h.o(i.atgm336h_init) refers to printfa.o(i.__0printf) for __2printf
    atgm336h.o(i.atgm336h_init) refers to usart.o(.bss) for huart3
    atgm336h.o(i.atgm336h_init) refers to atgm336h.o(.bss) for .bss
    atgm336h.o(i.atgm336h_process_data) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    atgm336h.o(i.atgm336h_process_data) refers to printfa.o(i.__0printf) for __2printf
    atgm336h.o(i.atgm336h_process_data) refers to atgm336h.o(i.parseGpsBuffer) for parseGpsBuffer
    atgm336h.o(i.atgm336h_process_data) refers to atgm336h.o(.data) for .data
    atgm336h.o(i.atgm336h_process_data) refers to atgm336h.o(.bss) for .bss
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to printfa.o(i.__0printf) for __2printf
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to memcpya.o(.text) for __aeabi_memcpy
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to usart.o(.bss) for huart3
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to atgm336h.o(.data) for .data
    atgm336h.o(i.atgm336h_usart3_irq_handler) refers to atgm336h.o(.bss) for .bss
    atgm336h.o(i.clrStruct) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    atgm336h.o(i.clrStruct) refers to memseta.o(.text) for __aeabi_memclr4
    atgm336h.o(i.clrStruct) refers to atgm336h.o(.bss) for .bss
    atgm336h.o(i.parseGpsBuffer) refers to strncpy.o(.text) for strncpy
    atgm336h.o(i.parseGpsBuffer) refers to strtok.o(.text) for strtok
    atgm336h.o(i.parseGpsBuffer) refers to strlen.o(.text) for strlen
    atgm336h.o(i.parseGpsBuffer) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    atgm336h.o(i.parseGpsBuffer) refers to atgm336h.o(.bss) for .bss
    atgm336h.o(i.printGpsBuffer) refers to printfa.o(i.__0printf) for __2printf
    atgm336h.o(i.printGpsBuffer) refers to strlen.o(.text) for strlen
    atgm336h.o(i.printGpsBuffer) refers to strstr.o(.text) for strstr
    atgm336h.o(i.printGpsBuffer) refers to strchr.o(.text) for strchr
    atgm336h.o(i.printGpsBuffer) refers to atgm336h.o(.bss) for .bss
    motor.o(i.Encoder_GetCount) refers to motor.o(.data) for .data
    motor.o(i.Encoder_GetDistanceMM) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    motor.o(i.Encoder_Init) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Reset) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Update) refers to motor.o(.data) for .data
    motor.o(i.Motor_Control) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(.data) refers to tim.o(.bss) for htim3
    servo.o(i.SERVO_Clamp) refers to servo.o(.data) for .data
    servo.o(i.SERVO_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    servo.o(i.SERVO_Init) refers to servo.o(.data) for .data
    servo.o(i.SERVO_Release) refers to servo.o(.data) for .data
    servo.o(i.SERVO_Stop) refers to servo.o(.data) for .data
    delay.o(i.delay_init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    delay.o(i.delay_init) refers to tim.o(.bss) for htim1
    delay.o(i.delay_ms) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    v831.o(i.clearV831Data) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    v831.o(i.clearV831Data) refers to memseta.o(.text) for __aeabi_memclr4
    v831.o(i.clearV831Data) refers to v831.o(.bss) for .bss
    v831.o(i.parseV831Buffer) refers to printfa.o(i.__0printf) for __2printf
    v831.o(i.parseV831Buffer) refers to strchr.o(.text) for strchr
    v831.o(i.parseV831Buffer) refers to strlen.o(.text) for strlen
    v831.o(i.parseV831Buffer) refers to strtok.o(.text) for strtok
    v831.o(i.parseV831Buffer) refers to atoi.o(.text) for atoi
    v831.o(i.parseV831Buffer) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    v831.o(i.parseV831Buffer) refers to strncpy.o(.text) for strncpy
    v831.o(i.parseV831Buffer) refers to memcpya.o(.text) for __aeabi_memcpy4
    v831.o(i.parseV831Buffer) refers to v831.o(.bss) for .bss
    v831.o(i.parseV831Buffer) refers to v831.o(.conststring) for .conststring
    v831.o(i.printV831Data) refers to printfa.o(i.__0printf) for __2printf
    v831.o(i.printV831Data) refers to v831.o(.bss) for .bss
    v831.o(i.printV831Data) refers to v831.o(.conststring) for .conststring
    v831.o(i.v831_init) refers to v831.o(i.clearV831Data) for clearV831Data
    v831.o(i.v831_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    v831.o(i.v831_init) refers to memseta.o(.text) for __aeabi_memclr4
    v831.o(i.v831_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    v831.o(i.v831_init) refers to main.o(i.Error_Handler) for Error_Handler
    v831.o(i.v831_init) refers to usart.o(.bss) for huart6
    v831.o(i.v831_init) refers to v831.o(.bss) for .bss
    v831.o(i.v831_process_data) refers to printfa.o(i.__0printf) for __2printf
    v831.o(i.v831_process_data) refers to v831.o(i.parseV831Buffer) for parseV831Buffer
    v831.o(i.v831_process_data) refers to v831.o(.data) for .data
    v831.o(i.v831_process_data) refers to v831.o(.bss) for .bss
    v831.o(i.v831_usart6_irq_handler) refers to printfa.o(i.__0printf) for __2printf
    v831.o(i.v831_usart6_irq_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    v831.o(i.v831_usart6_irq_handler) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    v831.o(i.v831_usart6_irq_handler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    v831.o(i.v831_usart6_irq_handler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    v831.o(i.v831_usart6_irq_handler) refers to usart.o(.bss) for huart6
    v831.o(i.v831_usart6_irq_handler) refers to v831.o(.data) for .data
    v831.o(i.v831_usart6_irq_handler) refers to v831.o(.bss) for .bss
    vl53l0x_i2c_dev.o(i.VL53L0X_GetValue) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) for VL53L0X_PerformSingleMeasurement
    vl53l0x_i2c_dev.o(i.VL53L0X_GetValue) refers to vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) for VL53L0X_GetRangingMeasurementData
    vl53l0x_i2c_dev.o(i.VL53L0X_GetValue) refers to vl53l0x_i2c_dev.o(.data) for .data
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceAddress) for VL53L0X_SetDeviceAddress
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_api.o(i.VL53L0X_SetPowerMode) for VL53L0X_SetPowerMode
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) for VL53L0X_SetInterMeasurementPeriodMilliSeconds
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_api.o(i.VL53L0X_DataInit) for VL53L0X_DataInit
    vl53l0x_i2c_dev.o(i.VL53L0X_Init) refers to vl53l0x_i2c_dev.o(.data) for .data
    vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay) refers to delay.o(i.delay_ms) for delay_ms
    vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte) refers to st_i2c.o(i.ST_IICreadBytes) for ST_IICreadBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte) refers to st_i2c.o(i.ST_IICwriteByte) for ST_IICwriteByte
    vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) refers to st_i2c.o(i.ST_IICwriteByte) for ST_IICwriteByte
    vl53l0x_i2c_dev.o(i.VL53L0X_WrDWord) refers to st_i2c.o(i.ST_IICwriteBytes) for ST_IICwriteBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) refers to st_i2c.o(i.ST_IICwriteBytes) for ST_IICwriteBytes
    vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti) refers to st_i2c.o(i.ST_IICwriteBytes) for ST_IICwriteBytes
    vl53l0x_i2c_dev.o(.data) refers to vl53l0x_i2c_dev.o(.bss) for TestDev_s
    vl53l0x_i2c_dev.o(.data) refers to vl53l0x_i2c_dev.o(.bss) for TestData_s
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds) for VL53L0X_GetInterruptThresholds
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) for VL53L0X_load_tuning_settings
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api.o(.data) for .data
    vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) for VL53L0X_GetDeviceParameters
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) for VL53L0X_SetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_GetDeviceErrorStatus) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetDeviceErrorString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string) for VL53L0X_get_device_error_string
    vl53l0x_api.o(i.VL53L0X_GetDeviceInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_device_info) for VL53L0X_get_device_info
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) for VL53L0X_GetInterMeasurementPeriodMilliSeconds
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps) for VL53L0X_GetXTalkCompensationRateMegaCps
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) for VL53L0X_GetOffsetCalibrationDataMicroMeter
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) for VL53L0X_GetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable) for VL53L0X_GetWrapAroundCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds) for VL53L0X_GetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_GetFractionEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetGpioConfig) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord) for VL53L0X_RdDWord
    vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckCurrent) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info) for VL53L0X_get_limit_check_info
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) refers to vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus) for VL53L0X_GetInterruptMaskStatus
    vl53l0x_api.o(i.VL53L0X_GetMeasurementRefSignal) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds) refers to vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) for VL53L0X_get_measurement_timing_budget_micro_seconds
    vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter) for VL53L0X_get_offset_calibration_data_micro_meter
    vl53l0x_api.o(i.VL53L0X_GetPalErrorString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string) for VL53L0X_get_pal_error_string
    vl53l0x_api.o(i.VL53L0X_GetPalStateString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string) for VL53L0X_get_pal_state_string
    vl53l0x_api.o(i.VL53L0X_GetPowerMode) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetProductRevision) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetRangeStatusString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string) for VL53L0X_get_range_status_string
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti) for VL53L0X_ReadMulti
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) for VL53L0X_get_pal_range_status
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration) for VL53L0X_get_ref_calibration
    vl53l0x_api.o(i.VL53L0X_GetReferenceSpads) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) for VL53L0X_get_reference_spads
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) refers to vl53l0x_api.o(i.sequence_step_enabled) for sequence_step_enabled
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) refers to vl53l0x_api.o(i.sequence_step_enabled) for sequence_step_enabled
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepsInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info) for VL53L0X_get_sequence_steps_info
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) for VL53L0X_get_total_signal_rate
    vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) refers to vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) for VL53L0X_get_vcsel_pulse_period
    vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_PerformOffsetCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) for VL53L0X_perform_offset_calibration
    vl53l0x_api.o(i.VL53L0X_PerformRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) for VL53L0X_perform_ref_calibration
    vl53l0x_api.o(i.VL53L0X_PerformRefSpadManagement) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) for VL53L0X_perform_ref_spad_management
    vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) refers to vl53l0x_api.o(i.VL53L0X_StartMeasurement) for VL53L0X_StartMeasurement
    vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) refers to vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) for VL53L0X_measurement_poll_for_completion
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) for VL53L0X_PerformSingleMeasurement
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) for VL53L0X_GetRangingMeasurementData
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api.o(i.VL53L0X_PerformXTalkCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) for VL53L0X_perform_xtalk_calibration
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api.o(i.VL53L0X_SetDeviceAddress) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) for VL53L0X_SetInterMeasurementPeriodMilliSeconds
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) for VL53L0X_SetXTalkCompensationRateMegaCps
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) for VL53L0X_SetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) for VL53L0X_SetWrapAroundCheckEnable
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetDmaxCalParameters) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrDWord) for VL53L0X_WrDWord
    vl53l0x_api.o(i.VL53L0X_SetInterruptThresholds) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLinearityCorrectiveGain) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) refers to vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) for VL53L0X_set_measurement_timing_budget_micro_seconds
    vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter) for VL53L0X_set_offset_calibration_data_micro_meter
    vl53l0x_api.o(i.VL53L0X_SetPowerMode) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetPowerMode) refers to vl53l0x_api.o(i.VL53L0X_StaticInit) for VL53L0X_StaticInit
    vl53l0x_api.o(i.VL53L0X_SetRangeFractionEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration) for VL53L0X_set_ref_calibration
    vl53l0x_api.o(i.VL53L0X_SetReferenceSpads) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) for VL53L0X_set_reference_spads
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperFactor) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetVcselPulsePeriod) refers to vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) for VL53L0X_set_vcsel_pulse_period
    vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) for VL53L0X_CheckAndLoadInterruptSettings
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to memseta.o(.text) for __aeabi_memclr4
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) for VL53L0X_perform_ref_spad_management
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) for VL53L0X_set_reference_spads
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) for VL53L0X_load_tuning_settings
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_SetGpioConfig) for VL53L0X_SetGpioConfig
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) for VL53L0X_GetDeviceParameters
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetFractionEnable) for VL53L0X_GetFractionEnable
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) for VL53L0X_SetSequenceStepEnable
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(.data) for .data
    vl53l0x_api.o(i.VL53L0X_StopMeasurement) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StopMeasurement) refers to vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) for VL53L0X_CheckAndLoadInterruptSettings
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) for VL53L0X_GetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) refers to vl53l0x_api_calibration.o(i.get_ref_spad_map) for get_ref_spad_map
    vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) refers to vl53l0x_api_calibration.o(i.count_enabled_spads) for count_enabled_spads
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) for VL53L0X_GetSequenceStepEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) for VL53L0X_SetSequenceStepEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) for VL53L0X_perform_single_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) for VL53L0X_perform_vhv_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) for VL53L0X_perform_phase_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) for VL53L0X_perform_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.enable_ref_spads) for enable_ref_spads
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) for perform_ref_signal_measurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to memcpya.o(.text) for __aeabi_memcpy4
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.get_next_good_spad) for get_next_good_spad
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.enable_spad_bit) for enable_spad_bit
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.set_ref_spad_map) for set_ref_spad_map
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) for VL53L0X_measurement_poll_for_completion
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) for VL53L0X_perform_single_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable) for VL53L0X_SetXTalkCompensationEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) for VL53L0X_SetXTalkCompensationRateMegaCps
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_api_calibration.o(i.enable_ref_spads) for enable_ref_spads
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.get_next_good_spad) for get_next_good_spad
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.enable_spad_bit) for enable_spad_bit
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.set_ref_spad_map) for set_ref_spad_map
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.get_ref_spad_map) for get_ref_spad_map
    vl53l0x_api_calibration.o(i.get_ref_spad_map) refers to vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti) for VL53L0X_ReadMulti
    vl53l0x_api_calibration.o(i.is_aperture) refers to vl53l0x_api_calibration.o(.data) for .data
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_calibration.o(i.set_ref_spad_map) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti) for VL53L0X_WriteMulti
    vl53l0x_api_core.o(i.VL53L0X_calc_dmax) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) for VL53L0X_get_total_signal_rate
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) for VL53L0X_get_total_xtalk_rate
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) for VL53L0X_calc_timeout_mclks
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_calc_dmax) for VL53L0X_calc_dmax
    vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) refers to vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps) for VL53L0X_calc_macro_period_ps
    vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us) refers to vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps) for VL53L0X_calc_macro_period_ps
    vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) for VL53L0X_device_read_strobe
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord) for VL53L0X_RdDWord
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) for VL53L0X_calc_sigma_estimate
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) for VL53L0X_GetLimitCheckValue
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) for VL53L0X_get_total_xtalk_rate
    vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) refers to vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable) for VL53L0X_GetXTalkCompensationEnable
    vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period) for VL53L0X_decode_vcsel_period
    vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti) for VL53L0X_WriteMulti
    vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) refers to vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) for VL53L0X_GetMeasurementDataReady
    vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) refers to vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api_core.o(i.VL53L0X_quadrature_sum) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.VL53L0X_encode_vcsel_period) for VL53L0X_encode_vcsel_period
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) for VL53L0X_perform_phase_calibration
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_decode_timeout) for VL53L0X_decode_timeout
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us) for VL53L0X_calc_timeout_us
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) for VL53L0X_calc_timeout_mclks
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_encode_timeout) for VL53L0X_encode_timeout
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_i2c_dev.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_decode_timeout) for VL53L0X_decode_timeout
    vl53l0x_api_strings.o(i.VL53L0X_check_part_used) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api_strings.o(i.VL53L0X_check_part_used) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to vl53l0x_api_strings.o(i.VL53L0X_check_part_used) for VL53L0X_check_part_used
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to vl53l0x_i2c_dev.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info) refers to strcpy.o(.text) for strcpy
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to uart_printf.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to uart_printf.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to uart_printf.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to uart_printf.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to uart_printf.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to uart_printf.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to uart_printf.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to uart_printf.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to uart_printf.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to uart_printf.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to uart_printf.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to uart_printf.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to uart_printf.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to uart_printf.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to uart_printf.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to uart_printf.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to uart_printf.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to uart_printf.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to uart_printf.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to uart_printf.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to uart_printf.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to uart_printf.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to uart_printf.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to uart_printf.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to uart_printf.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to uart_printf.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to uart_printf.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to uart_printf.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to uart_printf.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to uart_printf.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to uart_printf.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to uart_printf.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to uart_printf.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to uart_printf.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to uart_printf.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to uart_printf.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to uart_printf.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to uart_printf.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to uart_printf.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to uart_printf.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to uart_printf.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to uart_printf.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to uart_printf.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to uart_printf.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32h723xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32h723xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32h723xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (72 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (40 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (180 bytes).
    Removing stm32h7xx_it.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (496 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (92 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (260 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (136 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq), (328 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (428 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (668 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation), (112 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation), (80 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation), (120 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program), (148 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT), (128 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(.bss), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (160 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC), (188 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (196 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (144 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank1), (20 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (184 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (308 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank1), (44 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (352 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ActivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_DeactivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback), (2 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_GetClearKey), (12 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IsSemTaken), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ReleaseAll), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_SetClearKey), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Take), (40 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (488 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1118 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (368 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (160 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (144 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (176 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (216 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (640 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (1128 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (116 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (38 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (86 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (52 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (376 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (94 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (208 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (268 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (188 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (246 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (102 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (154 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (176 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (128 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (136 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (48 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (34 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (132 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (72 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (136 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (100 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (12 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (96 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (84 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (124 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (70 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (58 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ADC2ALT_Rout0Config), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ADC2ALT_Rout1Config), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32h7xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (296 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (300 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (324 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (160 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (172 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (312 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (232 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (384 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAError), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt), (148 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITError), (288 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt), (244 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt), (592 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (316 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (524 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (200 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (236 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (48 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (18 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (268 bytes).
    Removing stm32h7xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (220 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (276 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (108 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (108 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (432 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (184 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (112 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (182 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (154 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (40 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GetChannelState), (54 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (290 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init), (98 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start), (304 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (540 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop), (118 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (198 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (182 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (110 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init), (98 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start), (292 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (548 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop), (176 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (256 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (240 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (248 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (94 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (144 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (148 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (168 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (548 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (176 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (256 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (240 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (224 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig), (148 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (160 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (204 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (128 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (160 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (128 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (72 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (58 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (220 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (200 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (252 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (208 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (224 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (424 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (268 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (172 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (102 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (124 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (224 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (424 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (268 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (172 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (116 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (96 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32h7xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_SendBreak), (50 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort), (256 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive), (172 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (188 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAPause), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DeInit), (70 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetState), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (144 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback), (68 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt), (64 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT), (272 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT), (82 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT), (78 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (102 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (72 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (304 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (140 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (320 bytes).
    Removing core_json.o(i.JSON_Iterate), (170 bytes).
    Removing core_json.o(i.iterate), (130 bytes).
    Removing dht11.o(.rev16_text), (4 bytes).
    Removing dht11.o(.revsh_text), (4 bytes).
    Removing dht11.o(.rrx_text), (6 bytes).
    Removing esp8266.o(.rev16_text), (4 bytes).
    Removing esp8266.o(.revsh_text), (4 bytes).
    Removing esp8266.o(.rrx_text), (6 bytes).
    Removing esp8266.o(i.esp8266_config_network), (136 bytes).
    Removing esp8266.o(i.esp8266_connect_server), (100 bytes).
    Removing esp8266.o(i.esp8266_init), (996 bytes).
    Removing esp8266.o(i.esp8266_reset), (112 bytes).
    Removing esp8266.o(i.esp8266_send_cmd), (116 bytes).
    Removing esp8266.o(i.esp8266_send_msg), (288 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing main_program.o(.rev16_text), (4 bytes).
    Removing main_program.o(.revsh_text), (4 bytes).
    Removing main_program.o(.rrx_text), (6 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_LOGO), (100 bytes).
    Removing oled.o(i.OLED_display_off), (30 bytes).
    Removing oled.o(i.OLED_display_on), (30 bytes).
    Removing oled.o(i.OLED_draw_line), (200 bytes).
    Removing time_handle.o(.rev16_text), (4 bytes).
    Removing time_handle.o(.revsh_text), (4 bytes).
    Removing time_handle.o(.rrx_text), (6 bytes).
    Removing uart_printf.o(.rev16_text), (4 bytes).
    Removing uart_printf.o(.revsh_text), (4 bytes).
    Removing uart_printf.o(.rrx_text), (6 bytes).
    Removing st_i2c.o(.rev16_text), (4 bytes).
    Removing st_i2c.o(.revsh_text), (4 bytes).
    Removing st_i2c.o(.rrx_text), (6 bytes).
    Removing st_i2c.o(i.IIC_ReadOneByte), (48 bytes).
    Removing st_i2c.o(i.ST_IICreadByte), (8 bytes).
    Removing st_i2c.o(i.ST_IICwriteBit), (52 bytes).
    Removing atgm336h.o(.rev16_text), (4 bytes).
    Removing atgm336h.o(.revsh_text), (4 bytes).
    Removing atgm336h.o(.rrx_text), (6 bytes).
    Removing atgm336h.o(i.atgm336h_process_data), (160 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i.Encoder_GetCount), (12 bytes).
    Removing motor.o(i.Encoder_Reset), (20 bytes).
    Removing servo.o(.rev16_text), (4 bytes).
    Removing servo.o(.revsh_text), (4 bytes).
    Removing servo.o(.rrx_text), (6 bytes).
    Removing servo.o(i.SERVO_Stop), (20 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing v831.o(.rev16_text), (4 bytes).
    Removing v831.o(.revsh_text), (4 bytes).
    Removing v831.o(.rrx_text), (6 bytes).
    Removing v831.o(i.v831_process_data), (112 bytes).
    Removing vl53l0x_i2c_dev.o(.rev16_text), (4 bytes).
    Removing vl53l0x_i2c_dev.o(.revsh_text), (4 bytes).
    Removing vl53l0x_i2c_dev.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_EnableInterruptMask), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceErrorStatus), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceErrorString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceMode), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDmaxCalParameters), (18 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetGpioConfig), (118 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetHistogramMeasurementData), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetHistogramMode), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckCurrent), (72 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckStatus), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLinearityCorrectiveGain), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMaxNumberOfROIZones), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMeasurementRefSignal), (42 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfLimitCheck), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfROIZones), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfSequenceSteps), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalErrorString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalSpecVersion), (26 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalState), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalStateString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPowerMode), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetProductRevision), (28 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetRangeStatusString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetRefCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetReferenceSpads), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepTimeout), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepsInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor), (48 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus), (130 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate), (34 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetTuningSettingBuffer), (18 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetUpperLimitMilliMeter), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetVersion), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformOffsetCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformRefCalibration), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformRefSpadManagement), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformSingleHistogramMeasurement), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformXTalkCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformXTalkMeasurement), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_ResetDevice), (90 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDeviceParameters), (116 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDmaxCalParameters), (48 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetGroupParamHold), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetHistogramMode), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetInterruptThresholds), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetLinearityCorrectiveGain), (36 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetNumberOfROIZones), (12 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetRangeFractionEnable), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetRefCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetReferenceSpads), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout), (90 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperFactor), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetTuningSettingBuffer), (40 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetVcselPulsePeriod), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable), (68 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable), (52 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps), (46 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_StopMeasurement), (86 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_WaitDeviceBooted), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_WaitDeviceReadyForNewMeasurement), (6 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment), (50 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration), (26 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads), (114 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration), (198 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration), (216 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter), (64 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration), (32 bytes).
    Removing vl53l0x_api_calibration.o(i.count_enabled_spads), (148 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_encode_vcsel_period), (10 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_quadrature_sum), (28 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_reverse_bytes), (32 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period), (546 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_check_part_used), (60 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string), (464 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_device_info), (180 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info), (204 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string), (596 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string), (192 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string), (148 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info), (96 bytes).

741 unused section(s) (total 61996 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ../Core/Src/system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ../clib/../cmprslib/lz77c.c              0x00000000   Number         0  __dclz77c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/abort.c          0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\ST_I2C.c          0x00000000   Number         0  st_i2c.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\VL53l0x_i2c_dev.c 0x00000000   Number         0  vl53l0x_i2c_dev.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\vl53l0x_api.c     0x00000000   Number         0  vl53l0x_api.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\vl53l0x_api_calibration.c 0x00000000   Number         0  vl53l0x_api_calibration.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\vl53l0x_api_core.c 0x00000000   Number         0  vl53l0x_api_core.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\vl53l0x_api_ranging.c 0x00000000   Number         0  vl53l0x_api_ranging.o ABSOLUTE
    HARDWARE\SimpleVL53L0X\vl53l0x_api_strings.c 0x00000000   Number         0  vl53l0x_api_strings.o ABSOLUTE
    HARDWARE\Src\Servo.c                     0x00000000   Number         0  servo.o ABSOLUTE
    HARDWARE\Src\atgm336h.c                  0x00000000   Number         0  atgm336h.o ABSOLUTE
    HARDWARE\Src\delay.c                     0x00000000   Number         0  delay.o ABSOLUTE
    HARDWARE\Src\motor.c                     0x00000000   Number         0  motor.o ABSOLUTE
    HARDWARE\Src\v831.c                      0x00000000   Number         0  v831.o ABSOLUTE
    HARDWARE\\SimpleVL53L0X\\ST_I2C.c        0x00000000   Number         0  st_i2c.o ABSOLUTE
    HARDWARE\\SimpleVL53L0X\\VL53l0x_i2c_dev.c 0x00000000   Number         0  vl53l0x_i2c_dev.o ABSOLUTE
    HARDWARE\\Src\\Servo.c                   0x00000000   Number         0  servo.o ABSOLUTE
    HARDWARE\\Src\\atgm336h.c                0x00000000   Number         0  atgm336h.o ABSOLUTE
    HARDWARE\\Src\\delay.c                   0x00000000   Number         0  delay.o ABSOLUTE
    HARDWARE\\Src\\motor.c                   0x00000000   Number         0  motor.o ABSOLUTE
    HARDWARE\\Src\\v831.c                    0x00000000   Number         0  v831.o ABSOLUTE
    USER\\dht11.c                            0x00000000   Number         0  dht11.o ABSOLUTE
    USER\\esp8266.c                          0x00000000   Number         0  esp8266.o ABSOLUTE
    USER\\led.c                              0x00000000   Number         0  led.o ABSOLUTE
    USER\\main_program.c                     0x00000000   Number         0  main_program.o ABSOLUTE
    USER\\oled.c                             0x00000000   Number         0  oled.o ABSOLUTE
    USER\\time_handle.c                      0x00000000   Number         0  time_handle.o ABSOLUTE
    USER\\uart_printf.c                      0x00000000   Number         0  uart_printf.o ABSOLUTE
    USER\core_json.c                         0x00000000   Number         0  core_json.o ABSOLUTE
    USER\dht11.c                             0x00000000   Number         0  dht11.o ABSOLUTE
    USER\esp8266.c                           0x00000000   Number         0  esp8266.o ABSOLUTE
    USER\led.c                               0x00000000   Number         0  led.o ABSOLUTE
    USER\main_program.c                      0x00000000   Number         0  main_program.o ABSOLUTE
    USER\oled.c                              0x00000000   Number         0  oled.o ABSOLUTE
    USER\time_handle.c                       0x00000000   Number         0  time_handle.o ABSOLUTE
    USER\uart_printf.c                       0x00000000   Number         0  uart_printf.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32h723xx.s                    0x00000000   Number         0  startup_stm32h723xx.o ABSOLUTE
    RESET                                    0x08000000   Section      716  startup_stm32h723xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080002cc   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080002cc   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080002d0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080002d4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080002d4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080002d4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080002dc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080002e0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080002e0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080002e0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080002e0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080002e4   Section       44  startup_stm32h723xx.o(.text)
    $v0                                      0x080002e4   Number         0  startup_stm32h723xx.o(.text)
    .text                                    0x08000310   Section        0  uldiv.o(.text)
    .text                                    0x08000372   Section        0  memcpya.o(.text)
    .text                                    0x08000396   Section        0  memseta.o(.text)
    .text                                    0x080003ba   Section        0  strstr.o(.text)
    .text                                    0x080003de   Section        0  strncpy.o(.text)
    .text                                    0x080003f6   Section        0  strchr.o(.text)
    .text                                    0x0800040a   Section        0  strlen.o(.text)
    .text                                    0x08000418   Section        0  strcpy.o(.text)
    .text                                    0x0800042c   Section        0  strtok.o(.text)
    .text                                    0x08000470   Section        0  __0sscanf.o(.text)
    .text                                    0x080004a8   Section        0  _scanf_int.o(.text)
    .text                                    0x080005f4   Section        0  _scanf_str.o(.text)
    .text                                    0x080006d4   Section        0  atoi.o(.text)
    .text                                    0x080006ee   Section        0  uidiv.o(.text)
    .text                                    0x0800071a   Section        0  llshl.o(.text)
    .text                                    0x08000738   Section        0  llushr.o(.text)
    .text                                    0x08000758   Section        0  _chval.o(.text)
    .text                                    0x08000774   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000775   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x0800079c   Section        0  _sgetc.o(.text)
    .text                                    0x080007dc   Section        0  strtod.o(.text)
    _local_sscanf                            0x080007dd   Thumb Code    54  strtod.o(.text)
    .text                                    0x08000878   Section        0  strtol.o(.text)
    .text                                    0x080008e8   Section        0  iusefp.o(.text)
    .text                                    0x080008e8   Section        0  dadd.o(.text)
    .text                                    0x08000a36   Section        0  dmul.o(.text)
    .text                                    0x08000b1a   Section        0  ddiv.o(.text)
    .text                                    0x08000bf8   Section        0  dfixul.o(.text)
    .text                                    0x08000c28   Section       48  cdrcmple.o(.text)
    .text                                    0x08000c58   Section       36  init.o(.text)
    .text                                    0x08000c7c   Section        0  llsshr.o(.text)
    .text                                    0x08000ca0   Section        0  ctype_o.o(.text)
    .text                                    0x08000ca8   Section        0  isspace_o.o(.text)
    .text                                    0x08000cbc   Section        0  _scanf.o(.text)
    .text                                    0x08000fec   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000fed   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x0800134c   Section        0  _strtoul.o(.text)
    .text                                    0x080013ea   Section        0  depilogue.o(.text)
    .text                                    0x080014a4   Section        0  dfltul.o(.text)
    .text                                    0x080014bc   Section        0  d2f.o(.text)
    .text                                    0x080014f4   Section        0  fepilogue.o(.text)
    .text                                    0x08001562   Section        0  __dclz77c.o(.text)
    i.BusFault_Handler                       0x080015c0   Section        0  stm32h7xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x080015c4   Section        0  stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream1_IRQHandler                0x080015d0   Section        0  stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080015dc   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080015dd   Thumb Code   168  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08001690   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08001691   Thumb Code   138  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x0800172c   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x0800172d   Thumb Code   104  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_CheckFifoParam                     0x080017a0   Section        0  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080017a1   Thumb Code    84  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080017f4   Section        0  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080017f5   Thumb Code   518  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08001a14   Section        0  stm32h7xx_it.o(i.DebugMon_Handler)
    i.Encoder_GetDistanceMM                  0x08001a18   Section        0  motor.o(i.Encoder_GetDistanceMM)
    i.Encoder_Init                           0x08001a34   Section        0  motor.o(i.Encoder_Init)
    i.Encoder_Update                         0x08001a5c   Section        0  motor.o(i.Encoder_Update)
    i.Error_Handler                          0x08001a90   Section        0  main.o(i.Error_Handler)
    i.ExitRun0Mode                           0x08001a94   Section        0  system_stm32h7xx.o(i.ExitRun0Mode)
    i.HAL_DMA_Abort                          0x08001ab0   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001e10   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetError                       0x08002094   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_GetError)
    i.HAL_DMA_IRQHandler                     0x08002098   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002780   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002b3c   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002dd8   Section        0  stm32h7xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002dfc   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003018   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08003022   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800302c   Section        0  stm32h7xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x08003038   Section        0  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08003090   Section        0  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x080030e4   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Transmit                0x080031a4   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_Mem_Read                       0x080032f0   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x08003454   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x080035ac   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x080036a0   Section        0  stm32h7xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080036b0   Section        0  stm32h7xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800370c   Section        0  stm32h7xx_hal.o(i.HAL_InitTick)
    i.HAL_MPU_ConfigRegion                   0x0800374c   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    i.HAL_MPU_Disable                        0x080037a8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    i.HAL_MPU_Enable                         0x080037c4   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    i.HAL_MspInit                            0x080037e8   Section        0  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003804   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003820   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003860   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ConfigSupply                 0x08003884   Section        0  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    i.HAL_RCCEx_GetD3PCLK1Freq               0x080038d8   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    i.HAL_RCCEx_GetPLL2ClockFreq             0x080038fc   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    i.HAL_RCCEx_GetPLL3ClockFreq             0x08003a40   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    i.HAL_RCCEx_PeriphCLKConfig              0x08003b84   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080044c0   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x0800471c   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08004760   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08004784   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080047a8   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080048e0   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08004e06   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x08004e2c   Section        0  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x08004e2e   Section        0  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08004e30   Section        0  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08004e34   Section        0  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08004eec   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004f50   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08004fc0   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x08005054   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x080050f4   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x080051f0   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x080052ac   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08005310   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x0800539e   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080053a0   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x080054e8   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08005538   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x0800553a   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x0800565e   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x080056c0   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x080056c2   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x080056c4   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x080057e8   Section        0  time_handle.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08005854   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_DisableFifoMode             0x08005856   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxEventCallback             0x08005896   Section        0  stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08005898   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x0800589a   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x080058e6   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08005932   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08005934   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_DMAStop                       0x08005936   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x080059c8   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080059cc   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08005d68   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08005dd4   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive                       0x0800603c   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Receive)
    i.HAL_UART_Receive_DMA                   0x08006124   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    i.HAL_UART_RxCpltCallback                0x0800616c   Section        0  main.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x0800616e   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08006170   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08006220   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08006222   Section        0  stm32h7xx_it.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x08006224   Section        0  stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x08006225   Thumb Code    34  stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsErrorOccurred                    0x08006248   Section        0  stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x08006249   Thumb Code   268  stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_RequestMemoryRead                  0x08006358   Section        0  stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x08006359   Thumb Code    94  stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x080063bc   Section        0  stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x080063bd   Thumb Code    94  stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_TransferConfig                     0x08006420   Section        0  stm32h7xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08006421   Thumb Code    44  stm32h7xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x08006450   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08006451   Thumb Code   124  stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x080064cc   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x080064cd   Thumb Code    88  stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x08006524   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x08006525   Thumb Code    92  stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.JSON_SearchConst                       0x08006580   Section        0  core_json.o(i.JSON_SearchConst)
    i.JSON_SearchT                           0x080065e4   Section        0  core_json.o(i.JSON_SearchT)
    i.JSON_Validate                          0x080065f6   Section        0  core_json.o(i.JSON_Validate)
    i.MX_DMA_Init                            0x08006654   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08006690   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08006758   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080067b0   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x08006808   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08006874   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080068d8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08006940   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_USART1_UART_Init                    0x080069dc   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08006a44   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08006aac   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x08006b14   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08006b7c   Section        0  stm32h7xx_it.o(i.MemManage_Handler)
    i.Motor_Control                          0x08006b80   Section        0  motor.o(i.Motor_Control)
    i.Motor_Init                             0x08006bbc   Section        0  motor.o(i.Motor_Init)
    i.NMI_Handler                            0x08006bbe   Section        0  stm32h7xx_it.o(i.NMI_Handler)
    i.OLED_draw_point                        0x08006bc0   Section        0  oled.o(i.OLED_draw_point)
    i.OLED_init                              0x08006c08   Section        0  oled.o(i.OLED_init)
    i.OLED_operate_gram                      0x08006cf0   Section        0  oled.o(i.OLED_operate_gram)
    i.OLED_printf                            0x08006d30   Section        0  oled.o(i.OLED_printf)
    i.OLED_refresh_gram                      0x08006d74   Section        0  oled.o(i.OLED_refresh_gram)
    i.OLED_set_pos                           0x08006da8   Section        0  oled.o(i.OLED_set_pos)
    i.OLED_show_char                         0x08006dd0   Section        0  oled.o(i.OLED_show_char)
    i.OLED_show_string                       0x08006e48   Section        0  oled.o(i.OLED_show_string)
    i.PendSV_Handler                         0x08006e7e   Section        0  stm32h7xx_it.o(i.PendSV_Handler)
    i.RCCEx_PLL2_Config                      0x08006e80   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    RCCEx_PLL2_Config                        0x08006e81   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    i.RCCEx_PLL3_Config                      0x08006fa0   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    RCCEx_PLL3_Config                        0x08006fa1   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    i.SERVO_Clamp                            0x080070c0   Section        0  servo.o(i.SERVO_Clamp)
    i.SERVO_Init                             0x080070d4   Section        0  servo.o(i.SERVO_Init)
    i.SERVO_Release                          0x080070f0   Section        0  servo.o(i.SERVO_Release)
    i.ST_IICreadBytes                        0x08007104   Section        0  st_i2c.o(i.ST_IICreadBytes)
    i.ST_IICwriteByte                        0x08007128   Section        0  st_i2c.o(i.ST_IICwriteByte)
    i.ST_IICwriteBytes                       0x08007134   Section        0  st_i2c.o(i.ST_IICwriteBytes)
    i.SVC_Handler                            0x08007158   Section        0  stm32h7xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x0800715a   Section        0  stm32h7xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08007160   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080071f4   Section        0  system_stm32h7xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080072cc   Section        0  stm32h7xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x080072d8   Section        0  stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x080073b8   Section        0  stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x080073d2   Section        0  stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080073e4   Section        0  stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080073e5   Thumb Code    16  stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080073f8   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080073f9   Thumb Code   140  stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x0800749c   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08007534   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08007535   Thumb Code   126  stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080075cc   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080075cd   Thumb Code    96  stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x08007644   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x08007645   Thumb Code    90  stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x080076b8   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x080076b9   Thumb Code    90  stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800772c   Section        0  stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800772d   Thumb Code    34  stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x0800774e   Section        0  stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x0800774f   Thumb Code    36  stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UARTEx_SetNbDataToProcess              0x08007774   Section        0  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x08007775   Thumb Code    62  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x080077b8   Section        0  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08007880   Section        0  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x0800792a   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x0800792b   Thumb Code    16  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x0800793a   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x0800793b   Thumb Code    78  stm32h7xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08007988   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08007989   Thumb Code   130  stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08007a0a   Section        0  stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08007a0b   Thumb Code    32  stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08007a2c   Section        0  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08007a2d   Thumb Code    78  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08007a80   Section        0  stm32h7xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08007a81   Thumb Code    46  stm32h7xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_SetConfig                         0x08007ab0   Section        0  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08007e8c   Section        0  stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x08007f34   Section        0  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08007fc8   Section        0  stm32h7xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08007fdc   Section        0  stm32h7xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x08007ff4   Section        0  stm32h7xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x0800800c   Section        0  stm32h7xx_it.o(i.UsageFault_Handler)
    i.VL53L0X_CheckAndLoadInterruptSettings  0x08008010   Section        0  vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings)
    i.VL53L0X_ClearInterruptMask             0x0800808c   Section        0  vl53l0x_api.o(i.VL53L0X_ClearInterruptMask)
    i.VL53L0X_DataInit                       0x080080d8   Section        0  vl53l0x_api.o(i.VL53L0X_DataInit)
    i.VL53L0X_GetDeviceParameters            0x08008264   Section        0  vl53l0x_api.o(i.VL53L0X_GetDeviceParameters)
    i.VL53L0X_GetFractionEnable              0x080082e4   Section        0  vl53l0x_api.o(i.VL53L0X_GetFractionEnable)
    i.VL53L0X_GetInterMeasurementPeriodMilliSeconds 0x080082fe   Section        0  vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds)
    i.VL53L0X_GetInterruptMaskStatus         0x08008334   Section        0  vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus)
    i.VL53L0X_GetInterruptThresholds         0x08008356   Section        0  vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds)
    i.VL53L0X_GetLimitCheckEnable            0x08008390   Section        0  vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable)
    i.VL53L0X_GetLimitCheckValue             0x080083ac   Section        0  vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue)
    i.VL53L0X_GetMeasurementDataReady        0x0800842a   Section        0  vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady)
    i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds 0x08008464   Section        0  vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds)
    i.VL53L0X_GetOffsetCalibrationDataMicroMeter 0x08008468   Section        0  vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter)
    i.VL53L0X_GetRangingMeasurementData      0x0800846c   Section        0  vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData)
    i.VL53L0X_GetSequenceStepEnables         0x080085ac   Section        0  vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables)
    i.VL53L0X_GetValue                       0x08008620   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_GetValue)
    i.VL53L0X_GetVcselPulsePeriod            0x0800863c   Section        0  vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod)
    i.VL53L0X_GetWrapAroundCheckEnable       0x08008640   Section        0  vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable)
    i.VL53L0X_GetXTalkCompensationEnable     0x0800866c   Section        0  vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable)
    i.VL53L0X_GetXTalkCompensationRateMegaCps 0x08008674   Section        0  vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps)
    i.VL53L0X_Init                           0x080086a0   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_Init)
    i.VL53L0X_PerformSingleMeasurement       0x080086d8   Section        0  vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement)
    i.VL53L0X_PerformSingleRangingMeasurement 0x080086fa   Section        0  vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement)
    i.VL53L0X_PollingDelay                   0x0800872e   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay)
    i.VL53L0X_RdByte                         0x0800873a   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_RdByte)
    i.VL53L0X_RdDWord                        0x0800874c   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord)
    i.VL53L0X_RdWord                         0x08008778   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_RdWord)
    i.VL53L0X_ReadMulti                      0x08008798   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti)
    i.VL53L0X_SetDeviceAddress               0x080087ac   Section        0  vl53l0x_api.o(i.VL53L0X_SetDeviceAddress)
    i.VL53L0X_SetDeviceMode                  0x080087b4   Section        0  vl53l0x_api.o(i.VL53L0X_SetDeviceMode)
    i.VL53L0X_SetGpioConfig                  0x080087da   Section        0  vl53l0x_api.o(i.VL53L0X_SetGpioConfig)
    i.VL53L0X_SetInterMeasurementPeriodMilliSeconds 0x08008912   Section        0  vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds)
    i.VL53L0X_SetLimitCheckEnable            0x08008944   Section        0  vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable)
    i.VL53L0X_SetLimitCheckValue             0x080089ca   Section        0  vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue)
    i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds 0x08008a1a   Section        0  vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds)
    i.VL53L0X_SetPowerMode                   0x08008a1e   Section        0  vl53l0x_api.o(i.VL53L0X_SetPowerMode)
    i.VL53L0X_SetSequenceStepEnable          0x08008a66   Section        0  vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable)
    i.VL53L0X_StartMeasurement               0x08008b18   Section        0  vl53l0x_api.o(i.VL53L0X_StartMeasurement)
    i.VL53L0X_StaticInit                     0x08008bec   Section        0  vl53l0x_api.o(i.VL53L0X_StaticInit)
    i.VL53L0X_UpdateByte                     0x08008d60   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte)
    i.VL53L0X_WrByte                         0x08008d90   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_WrByte)
    i.VL53L0X_WrDWord                        0x08008d9e   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_WrDWord)
    i.VL53L0X_WrWord                         0x08008dc6   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_WrWord)
    i.VL53L0X_WriteMulti                     0x08008de2   Section        0  vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti)
    i.VL53L0X_calc_dmax                      0x08008df8   Section        0  vl53l0x_api_core.o(i.VL53L0X_calc_dmax)
    i.VL53L0X_calc_macro_period_ps           0x08008ed8   Section        0  vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps)
    i.VL53L0X_calc_sigma_estimate            0x08008ee8   Section        0  vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate)
    i.VL53L0X_calc_timeout_mclks             0x08009120   Section        0  vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks)
    i.VL53L0X_calc_timeout_us                0x08009142   Section        0  vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us)
    i.VL53L0X_decode_timeout                 0x08009164   Section        0  vl53l0x_api_core.o(i.VL53L0X_decode_timeout)
    i.VL53L0X_decode_vcsel_period            0x08009170   Section        0  vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period)
    i.VL53L0X_device_read_strobe             0x08009178   Section        0  vl53l0x_api_core.o(i.VL53L0X_device_read_strobe)
    i.VL53L0X_encode_timeout                 0x080091c2   Section        0  vl53l0x_api_core.o(i.VL53L0X_encode_timeout)
    i.VL53L0X_get_info_from_device           0x080091e2   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_info_from_device)
    i.VL53L0X_get_measurement_timing_budget_micro_seconds 0x0800970c   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds)
    i.VL53L0X_get_offset_calibration_data_micro_meter 0x080097d2   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter)
    i.VL53L0X_get_pal_range_status           0x08009808   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status)
    i.VL53L0X_get_total_signal_rate          0x080099ea   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate)
    i.VL53L0X_get_total_xtalk_rate           0x08009a06   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate)
    i.VL53L0X_get_vcsel_pulse_period         0x08009a34   Section        0  vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period)
    i.VL53L0X_isqrt                          0x08009a64   Section        0  vl53l0x_api_core.o(i.VL53L0X_isqrt)
    i.VL53L0X_load_tuning_settings           0x08009a8e   Section        0  vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings)
    i.VL53L0X_measurement_poll_for_completion 0x08009b32   Section        0  vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion)
    i.VL53L0X_perform_phase_calibration      0x08009b68   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration)
    i.VL53L0X_perform_ref_calibration        0x08009bdc   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration)
    i.VL53L0X_perform_ref_spad_management    0x08009c1c   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management)
    i.VL53L0X_perform_single_ref_calibration 0x08009e38   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration)
    i.VL53L0X_perform_vhv_calibration        0x08009e70   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration)
    i.VL53L0X_ref_calibration_io             0x08009ee4   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io)
    i.VL53L0X_set_measurement_timing_budget_micro_seconds 0x08009f96   Section        0  vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds)
    i.VL53L0X_set_reference_spads            0x0800a05a   Section        0  vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads)
    i.__0printf                              0x0800a100   Section        0  printfa.o(i.__0printf)
    i.__0vsprintf                            0x0800a120   Section        0  printfa.o(i.__0vsprintf)
    i.__NVIC_SetPriority                     0x0800a144   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x0800a145   Thumb Code    34  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__aeabi_errno_addr                     0x0800a168   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_atof                          0x0800a170   Section        0  atof.o(i.__hardfp_atof)
    i.__read_errno                           0x0800a1a8   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x0800a1b4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800a1c2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800a1c4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800a1d4   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0800a1e0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800a1e1   Thumb Code   366  printfa.o(i._fp_digits)
    i._is_digit                              0x0800a364   Section        0  scanf_fp.o(i._is_digit)
    i._printf_core                           0x0800a374   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800a375   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800aa28   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800aa29   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800aa4c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800aa4d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x0800aa7a   Section        0  printfa.o(i._sputc)
    _sputc                                   0x0800aa7b   Thumb Code    10  printfa.o(i._sputc)
    i.arraySearch                            0x0800aa84   Section        0  core_json.o(i.arraySearch)
    arraySearch                              0x0800aa85   Thumb Code   156  core_json.o(i.arraySearch)
    i.atgm336h_init                          0x0800ab20   Section        0  atgm336h.o(i.atgm336h_init)
    i.atgm336h_usart3_irq_handler            0x0800ab88   Section        0  atgm336h.o(i.atgm336h_usart3_irq_handler)
    i.clearV831Data                          0x0800ae34   Section        0  v831.o(i.clearV831Data)
    i.clrStruct                              0x0800ae68   Section        0  atgm336h.o(i.clrStruct)
    i.delay_init                             0x0800aed8   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800aee4   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x0800aee8   Section        0  delay.o(i.delay_us)
    i.dht11_check                            0x0800af0c   Section        0  dht11.o(i.dht11_check)
    i.dht11_init                             0x0800af64   Section        0  dht11.o(i.dht11_init)
    i.dht11_io_in                            0x0800af80   Section        0  dht11.o(i.dht11_io_in)
    i.dht11_io_out                           0x0800afa8   Section        0  dht11.o(i.dht11_io_out)
    i.dht11_read_bit                         0x0800afd0   Section        0  dht11.o(i.dht11_read_bit)
    i.dht11_read_byte                        0x0800b028   Section        0  dht11.o(i.dht11_read_byte)
    i.dht11_read_data                        0x0800b042   Section        0  dht11.o(i.dht11_read_data)
    i.dht11_rst                              0x0800b094   Section        0  dht11.o(i.dht11_rst)
    i.enable_ref_spads                       0x0800b0c4   Section        0  vl53l0x_api_calibration.o(i.enable_ref_spads)
    i.enable_spad_bit                        0x0800b158   Section        0  vl53l0x_api_calibration.o(i.enable_spad_bit)
    i.esp8266_receive_msg                    0x0800b184   Section        0  esp8266.o(i.esp8266_receive_msg)
    i.fputc                                  0x0800b220   Section        0  uart_printf.o(i.fputc)
    i.getType                                0x0800b238   Section        0  core_json.o(i.getType)
    getType                                  0x0800b239   Thumb Code    56  core_json.o(i.getType)
    i.get_next_good_spad                     0x0800b270   Section        0  vl53l0x_api_calibration.o(i.get_next_good_spad)
    i.get_ref_spad_map                       0x0800b2d0   Section        0  vl53l0x_api_calibration.o(i.get_ref_spad_map)
    i.get_sequence_step_timeout              0x0800b2da   Section        0  vl53l0x_api_core.o(i.get_sequence_step_timeout)
    i.hexToInt                               0x0800b3ce   Section        0  core_json.o(i.hexToInt)
    hexToInt                                 0x0800b3cf   Thumb Code    34  core_json.o(i.hexToInt)
    i.is_aperture                            0x0800b3f0   Section        0  vl53l0x_api_calibration.o(i.is_aperture)
    i.main                                   0x0800b408   Section        0  main.o(i.main)
    i.multiSearch                            0x0800b496   Section        0  core_json.o(i.multiSearch)
    multiSearch                              0x0800b497   Thumb Code   252  core_json.o(i.multiSearch)
    i.nextKeyValuePair                       0x0800b592   Section        0  core_json.o(i.nextKeyValuePair)
    nextKeyValuePair                         0x0800b593   Thumb Code   152  core_json.o(i.nextKeyValuePair)
    i.nextValue                              0x0800b62a   Section        0  core_json.o(i.nextValue)
    nextValue                                0x0800b62b   Thumb Code   100  core_json.o(i.nextValue)
    i.objectSearch                           0x0800b68e   Section        0  core_json.o(i.objectSearch)
    objectSearch                             0x0800b68f   Thumb Code   178  core_json.o(i.objectSearch)
    i.oled_write_byte                        0x0800b740   Section        0  oled.o(i.oled_write_byte)
    i.parseGpsBuffer                         0x0800b76c   Section        0  atgm336h.o(i.parseGpsBuffer)
    i.parseV831Buffer                        0x0800b91c   Section        0  v831.o(i.parseV831Buffer)
    i.parse_json_msg                         0x0800bc7c   Section        0  esp8266.o(i.parse_json_msg)
    i.perform_ref_signal_measurement         0x0800bd0c   Section        0  vl53l0x_api_calibration.o(i.perform_ref_signal_measurement)
    i.printGpsBuffer                         0x0800bd70   Section        0  atgm336h.o(i.printGpsBuffer)
    i.printV831Data                          0x0800c02c   Section        0  v831.o(i.printV831Data)
    i.sequence_step_enabled                  0x0800c094   Section        0  vl53l0x_api.o(i.sequence_step_enabled)
    i.set_led                                0x0800c0cc   Section        0  led.o(i.set_led)
    i.set_ref_spad_map                       0x0800c0e4   Section        0  vl53l0x_api_calibration.o(i.set_ref_spad_map)
    i.set_sequence_step_timeout              0x0800c0ee   Section        0  vl53l0x_api_core.o(i.set_sequence_step_timeout)
    i.skipAnyScalar                          0x0800c1f4   Section        0  core_json.o(i.skipAnyScalar)
    skipAnyScalar                            0x0800c1f5   Thumb Code    90  core_json.o(i.skipAnyScalar)
    i.skipCollection                         0x0800c268   Section        0  core_json.o(i.skipCollection)
    skipCollection                           0x0800c269   Thumb Code   168  core_json.o(i.skipCollection)
    i.skipDigits                             0x0800c310   Section        0  core_json.o(i.skipDigits)
    skipDigits                               0x0800c311   Thumb Code   116  core_json.o(i.skipDigits)
    i.skipEscape                             0x0800c388   Section        0  core_json.o(i.skipEscape)
    skipEscape                               0x0800c389   Thumb Code   180  core_json.o(i.skipEscape)
    i.skipLiteral                            0x0800c43c   Section        0  core_json.o(i.skipLiteral)
    skipLiteral                              0x0800c43d   Thumb Code    58  core_json.o(i.skipLiteral)
    i.skipNumber                             0x0800c476   Section        0  core_json.o(i.skipNumber)
    skipNumber                               0x0800c477   Thumb Code   182  core_json.o(i.skipNumber)
    i.skipObjectScalars                      0x0800c52c   Section        0  core_json.o(i.skipObjectScalars)
    skipObjectScalars                        0x0800c52d   Thumb Code   136  core_json.o(i.skipObjectScalars)
    i.skipOneHexEscape                       0x0800c5b4   Section        0  core_json.o(i.skipOneHexEscape)
    skipOneHexEscape                         0x0800c5b5   Thumb Code   112  core_json.o(i.skipOneHexEscape)
    i.skipScalars                            0x0800c624   Section        0  core_json.o(i.skipScalars)
    skipScalars                              0x0800c625   Thumb Code    92  core_json.o(i.skipScalars)
    i.skipSpace                              0x0800c680   Section        0  core_json.o(i.skipSpace)
    skipSpace                                0x0800c681   Thumb Code    44  core_json.o(i.skipSpace)
    i.skipSpaceAndComma                      0x0800c6ac   Section        0  core_json.o(i.skipSpaceAndComma)
    skipSpaceAndComma                        0x0800c6ad   Thumb Code    80  core_json.o(i.skipSpaceAndComma)
    i.skipString                             0x0800c6fc   Section        0  core_json.o(i.skipString)
    skipString                               0x0800c6fd   Thumb Code    98  core_json.o(i.skipString)
    i.strnEq                                 0x0800c75e   Section        0  core_json.o(i.strnEq)
    strnEq                                   0x0800c75f   Thumb Code    40  core_json.o(i.strnEq)
    i.time_slot_start                        0x0800c788   Section        0  time_handle.o(i.time_slot_start)
    i.uart2_receiver_clear                   0x0800c794   Section        0  esp8266.o(i.uart2_receiver_clear)
    i.uart2_receiver_handle                  0x0800c7b4   Section        0  esp8266.o(i.uart2_receiver_handle)
    i.user_init_program                      0x0800c7fc   Section        0  main_program.o(i.user_init_program)
    i.user_main_program                      0x0800c8b8   Section        0  main_program.o(i.user_main_program)
    i.v831_init                              0x0800ca5c   Section        0  v831.o(i.v831_init)
    i.v831_usart6_irq_handler                0x0800ca9c   Section        0  v831.o(i.v831_usart6_irq_handler)
    .constdata                               0x0800cce8   Section        8  stm32h7xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800cce8   Data           8  stm32h7xx_hal_dma.o(.constdata)
    .constdata                               0x0800ccf0   Section       24  stm32h7xx_hal_uart.o(.constdata)
    .constdata                               0x0800cd08   Section       16  stm32h7xx_hal_uart_ex.o(.constdata)
    numerator                                0x0800cd08   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    denominator                              0x0800cd10   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    .constdata                               0x0800cd18   Section       16  system_stm32h7xx.o(.constdata)
    .constdata                               0x0800cd28   Section     2164  oled.o(.constdata)
    .constdata                               0x0800d59c   Section      129  ctype_o.o(.constdata)
    .constdata                               0x0800d620   Section        4  ctype_o.o(.constdata)
    table                                    0x0800d620   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0800d624   Section       78  esp8266.o(.conststring)
    .conststring                             0x0800d674   Section      210  v831.o(.conststring)
    .data                                    0x24000000   Section       12  stm32h7xx_hal.o(.data)
    .data                                    0x2400000c   Section        8  system_stm32h7xx.o(.data)
    .data                                    0x24000014   Section        6  esp8266.o(.data)
    error_count                              0x24000015   Data           1  esp8266.o(.data)
    .data                                    0x2400001c   Section       12  main_program.o(.data)
    .data                                    0x24000028   Section        8  oled.o(.data)
    cmd_data                                 0x24000028   Data           2  oled.o(.data)
    ap                                       0x2400002c   Data           4  oled.o(.data)
    .data                                    0x24000030   Section       16  time_handle.o(.data)
    index_10ms                               0x24000032   Data           2  time_handle.o(.data)
    .data                                    0x24000040   Section        8  atgm336h.o(.data)
    last_process_time                        0x24000044   Data           4  atgm336h.o(.data)
    .data                                    0x24000048   Section       12  motor.o(.data)
    encoder_last_count                       0x24000048   Data           2  motor.o(.data)
    encoder_htim                             0x2400004c   Data           4  motor.o(.data)
    encoder_total_count                      0x24000050   Data           4  motor.o(.data)
    .data                                    0x24000054   Section        4  servo.o(.data)
    servo_htim                               0x24000054   Data           4  servo.o(.data)
    .data                                    0x24000058   Section        4  v831.o(.data)
    .data                                    0x2400005c   Section        8  vl53l0x_i2c_dev.o(.data)
    TestDev                                  0x2400005c   Data           4  vl53l0x_i2c_dev.o(.data)
    TestData                                 0x24000060   Data           4  vl53l0x_i2c_dev.o(.data)
    .data                                    0x24000064   Section      681  vl53l0x_api.o(.data)
    .data                                    0x24000310   Section       16  vl53l0x_api_calibration.o(.data)
    .data                                    0x24000320   Section        4  strtok.o(.data)
    state                                    0x24000320   Data           4  strtok.o(.data)
    .data                                    0x24000324   Section        4  stdout.o(.data)
    .data                                    0x24000328   Section        4  errno.o(.data)
    _errno                                   0x24000328   Data           4  errno.o(.data)
    .bss                                     0x2400032c   Section      168  i2c.o(.bss)
    .bss                                     0x240003d4   Section      304  tim.o(.bss)
    .bss                                     0x24000504   Section      832  usart.o(.bss)
    .bss                                     0x24000844   Section      512  esp8266.o(.bss)
    .bss                                     0x24000a44   Section     1152  oled.o(.bss)
    LCD_BUF                                  0x24000a44   Data         128  oled.o(.bss)
    OLED_GRAM                                0x24000ac4   Data        1024  oled.o(.bss)
    .bss                                     0x24000ec4   Section      448  atgm336h.o(.bss)
    .bss                                     0x24001084   Section     1680  v831.o(.bss)
    .bss                                     0x24001714   Section      352  vl53l0x_i2c_dev.o(.bss)
    TestDev_s                                0x24001714   Data         352  vl53l0x_i2c_dev.o(.bss)
    .bss                                     0x24001874   Section       28  vl53l0x_i2c_dev.o(.bss)
    TestData_s                               0x24001874   Data          28  vl53l0x_i2c_dev.o(.bss)
    STACK                                    0x24001890   Section     1024  startup_stm32h723xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    __Vectors_Size                           0x000002cc   Number         0  startup_stm32h723xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h723xx.o(RESET)
    __Vectors_End                            0x080002cc   Data           0  startup_stm32h723xx.o(RESET)
    __main                                   0x080002cd   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080002cd   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080002d1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080002d5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080002d5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080002d5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080002d5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080002dd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080002e1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080002e1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080002e5   Thumb Code    12  startup_stm32h723xx.o(.text)
    ADC3_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    ADC_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel0_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel1_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel2_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel3_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel4_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel5_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel6_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel7_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    CEC_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    COMP1_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    CORDIC_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    CRS_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DCMI_PSSI_IRQHandler                     0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2D_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    DTS_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    ECC_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI0_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI1_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI2_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI3_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI4_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT0_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT1_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN_CAL_IRQHandler                     0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FLASH_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMAC_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMC_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    FPU_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    HSEM1_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_EV_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_EV_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM1_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM2_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM3_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM4_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM5_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPUART1_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_ER_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_WKUP_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDMA_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI1_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI2_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    PVD_AVD_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    RCC_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    RNG_IRQHandler                           0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI1_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI4_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC1_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC2_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPDIF_RX_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI1_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI2_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI3_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI4_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI5_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI6_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    SWPMI1_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM15_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM16_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM17_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_BRK_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_UP_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM23_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM24_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM3_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM4_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM5_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM7_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART4_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART5_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART7_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART8_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART9_IRQHandler                         0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART10_IRQHandler                       0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART1_IRQHandler                        0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    WAKEUP_PIN_IRQHandler                    0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    WWDG_IRQHandler                          0x08000303   Thumb Code     0  startup_stm32h723xx.o(.text)
    __aeabi_uldivmod                         0x08000311   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000373   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000373   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000373   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000397   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000397   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000397   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080003a5   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080003a5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080003a5   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080003a9   Thumb Code    18  memseta.o(.text)
    strstr                                   0x080003bb   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x080003df   Thumb Code    24  strncpy.o(.text)
    strchr                                   0x080003f7   Thumb Code    20  strchr.o(.text)
    strlen                                   0x0800040b   Thumb Code    14  strlen.o(.text)
    strcpy                                   0x08000419   Thumb Code    18  strcpy.o(.text)
    strtok                                   0x0800042d   Thumb Code    62  strtok.o(.text)
    __0sscanf                                0x08000471   Thumb Code    48  __0sscanf.o(.text)
    _scanf_int                               0x080004a9   Thumb Code   332  _scanf_int.o(.text)
    _scanf_string                            0x080005f5   Thumb Code   224  _scanf_str.o(.text)
    atoi                                     0x080006d5   Thumb Code    26  atoi.o(.text)
    __aeabi_uidiv                            0x080006ef   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080006ef   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800071b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800071b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000739   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000739   Thumb Code     0  llushr.o(.text)
    _chval                                   0x08000759   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000781   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x0800079d   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080007bb   Thumb Code    34  _sgetc.o(.text)
    __strtod_int                             0x08000813   Thumb Code    90  strtod.o(.text)
    strtol                                   0x08000879   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x080008e9   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080008e9   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000a2b   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000a31   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000a37   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000b1b   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000bf9   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000c29   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000c59   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000c59   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000c7d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000c7d   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000ca1   Thumb Code     4  ctype_o.o(.text)
    isspace                                  0x08000ca9   Thumb Code    18  isspace_o.o(.text)
    __vfscanf                                0x08000cbd   Thumb Code   810  _scanf.o(.text)
    _scanf_real                              0x08001115   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x08001115   Thumb Code   556  scanf_fp.o(.text)
    _strtoul                                 0x0800134d   Thumb Code   158  _strtoul.o(.text)
    _double_round                            0x080013eb   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08001409   Thumb Code   156  depilogue.o(.text)
    __aeabi_ul2d                             0x080014a5   Thumb Code    24  dfltul.o(.text)
    __aeabi_d2f                              0x080014bd   Thumb Code    56  d2f.o(.text)
    _float_round                             0x080014f5   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08001507   Thumb Code    92  fepilogue.o(.text)
    __decompress                             0x08001563   Thumb Code     0  __dclz77c.o(.text)
    __decompress2                            0x08001563   Thumb Code    94  __dclz77c.o(.text)
    BusFault_Handler                         0x080015c1   Thumb Code     2  stm32h7xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x080015c5   Thumb Code     6  stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream1_IRQHandler                  0x080015d1   Thumb Code     6  stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler)
    DebugMon_Handler                         0x08001a15   Thumb Code     2  stm32h7xx_it.o(i.DebugMon_Handler)
    Encoder_GetDistanceMM                    0x08001a19   Thumb Code    20  motor.o(i.Encoder_GetDistanceMM)
    Encoder_Init                             0x08001a35   Thumb Code    34  motor.o(i.Encoder_Init)
    Encoder_Update                           0x08001a5d   Thumb Code    46  motor.o(i.Encoder_Update)
    Error_Handler                            0x08001a91   Thumb Code     4  main.o(i.Error_Handler)
    ExitRun0Mode                             0x08001a95   Thumb Code    22  system_stm32h7xx.o(i.ExitRun0Mode)
    HAL_DMA_Abort                            0x08001ab1   Thumb Code   836  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001e11   Thumb Code   602  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetError                         0x08002095   Thumb Code     4  stm32h7xx_hal_dma.o(i.HAL_DMA_GetError)
    HAL_DMA_IRQHandler                       0x08002099   Thumb Code  1756  stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002781   Thumb Code   922  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002b3d   Thumb Code   628  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002dd9   Thumb Code    32  stm32h7xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002dfd   Thumb Code   496  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003019   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08003023   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800302d   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x08003039   Thumb Code    88  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08003091   Thumb Code    84  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x080030e5   Thumb Code   186  stm32h7xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Transmit                  0x080031a5   Thumb Code   324  stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_Mem_Read                         0x080032f1   Thumb Code   346  stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x08003455   Thumb Code   340  stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080035ad   Thumb Code   222  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x080036a1   Thumb Code    12  stm32h7xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080036b1   Thumb Code    74  stm32h7xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800370d   Thumb Code    56  stm32h7xx_hal.o(i.HAL_InitTick)
    HAL_MPU_ConfigRegion                     0x0800374d   Thumb Code    86  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    HAL_MPU_Disable                          0x080037a9   Thumb Code    24  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    HAL_MPU_Enable                           0x080037c5   Thumb Code    30  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    HAL_MspInit                              0x080037e9   Thumb Code    22  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003805   Thumb Code    26  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003821   Thumb Code    60  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003861   Thumb Code    28  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ConfigSupply                   0x08003885   Thumb Code    78  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    HAL_RCCEx_GetD3PCLK1Freq                 0x080038d9   Thumb Code    26  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    HAL_RCCEx_GetPLL2ClockFreq               0x080038fd   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    HAL_RCCEx_GetPLL3ClockFreq               0x08003a41   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    HAL_RCCEx_PeriphCLKConfig                0x08003b85   Thumb Code  2362  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080044c1   Thumb Code   580  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x0800471d   Thumb Code    52  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08004761   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08004785   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080047a9   Thumb Code   278  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080048e1   Thumb Code  1318  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08004e07   Thumb Code    38  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x08004e2d   Thumb Code     2  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x08004e2f   Thumb Code     2  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08004e31   Thumb Code     2  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08004e35   Thumb Code   146  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08004eed   Thumb Code    98  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004f51   Thumb Code    96  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08004fc1   Thumb Code   108  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08005055   Thumb Code   118  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x080050f5   Thumb Code   248  stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080051f1   Thumb Code   182  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x080052ad   Thumb Code    88  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08005311   Thumb Code   142  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x0800539f   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080053a1   Thumb Code   328  stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x080054e9   Thumb Code    68  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08005539   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800553b   Thumb Code   292  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x0800565f   Thumb Code    98  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080056c1   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x080056c3   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x080056c5   Thumb Code   244  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x080057e9   Thumb Code   100  time_handle.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08005855   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_DisableFifoMode               0x08005857   Thumb Code    64  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxEventCallback               0x08005897   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08005899   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x0800589b   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x080058e7   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08005933   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08005935   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_DMAStop                         0x08005937   Thumb Code   146  stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x080059c9   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080059cd   Thumb Code   898  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08005d69   Thumb Code   106  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08005dd5   Thumb Code   568  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive                         0x0800603d   Thumb Code   232  stm32h7xx_hal_uart.o(i.HAL_UART_Receive)
    HAL_UART_Receive_DMA                     0x08006125   Thumb Code    66  stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    HAL_UART_RxCpltCallback                  0x0800616d   Thumb Code     2  main.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800616f   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08006171   Thumb Code   176  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08006221   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08006223   Thumb Code     2  stm32h7xx_it.o(i.HardFault_Handler)
    JSON_SearchConst                         0x08006581   Thumb Code   100  core_json.o(i.JSON_SearchConst)
    JSON_SearchT                             0x080065e5   Thumb Code    18  core_json.o(i.JSON_SearchT)
    JSON_Validate                            0x080065f7   Thumb Code    94  core_json.o(i.JSON_Validate)
    MX_DMA_Init                              0x08006655   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08006691   Thumb Code   190  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08006759   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x080067b1   Thumb Code    76  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x08006809   Thumb Code    98  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08006875   Thumb Code    96  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080068d9   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08006941   Thumb Code   148  tim.o(i.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x080069dd   Thumb Code    94  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08006a45   Thumb Code    94  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08006aad   Thumb Code    94  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08006b15   Thumb Code    94  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08006b7d   Thumb Code     2  stm32h7xx_it.o(i.MemManage_Handler)
    Motor_Control                            0x08006b81   Thumb Code    56  motor.o(i.Motor_Control)
    Motor_Init                               0x08006bbd   Thumb Code     2  motor.o(i.Motor_Init)
    NMI_Handler                              0x08006bbf   Thumb Code     2  stm32h7xx_it.o(i.NMI_Handler)
    OLED_draw_point                          0x08006bc1   Thumb Code    68  oled.o(i.OLED_draw_point)
    OLED_init                                0x08006c09   Thumb Code   230  oled.o(i.OLED_init)
    OLED_operate_gram                        0x08006cf1   Thumb Code    60  oled.o(i.OLED_operate_gram)
    OLED_printf                              0x08006d31   Thumb Code    58  oled.o(i.OLED_printf)
    OLED_refresh_gram                        0x08006d75   Thumb Code    46  oled.o(i.OLED_refresh_gram)
    OLED_set_pos                             0x08006da9   Thumb Code    40  oled.o(i.OLED_set_pos)
    OLED_show_char                           0x08006dd1   Thumb Code   116  oled.o(i.OLED_show_char)
    OLED_show_string                         0x08006e49   Thumb Code    54  oled.o(i.OLED_show_string)
    PendSV_Handler                           0x08006e7f   Thumb Code     2  stm32h7xx_it.o(i.PendSV_Handler)
    SERVO_Clamp                              0x080070c1   Thumb Code    14  servo.o(i.SERVO_Clamp)
    SERVO_Init                               0x080070d5   Thumb Code    24  servo.o(i.SERVO_Init)
    SERVO_Release                            0x080070f1   Thumb Code    14  servo.o(i.SERVO_Release)
    ST_IICreadBytes                          0x08007105   Thumb Code    32  st_i2c.o(i.ST_IICreadBytes)
    ST_IICwriteByte                          0x08007129   Thumb Code    12  st_i2c.o(i.ST_IICwriteByte)
    ST_IICwriteBytes                         0x08007135   Thumb Code    32  st_i2c.o(i.ST_IICwriteBytes)
    SVC_Handler                              0x08007159   Thumb Code     2  stm32h7xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800715b   Thumb Code     4  stm32h7xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08007161   Thumb Code   144  main.o(i.SystemClock_Config)
    SystemInit                               0x080071f5   Thumb Code   184  system_stm32h7xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x080072cd   Thumb Code     6  stm32h7xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x080072d9   Thumb Code   182  stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x080073b9   Thumb Code    26  stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x080073d3   Thumb Code    18  stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x0800749d   Thumb Code   126  stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_AdvFeatureConfig                    0x080077b9   Thumb Code   200  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08007881   Thumb Code   170  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08007ab1   Thumb Code   914  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_DMA                   0x08007e8d   Thumb Code   156  stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_WaitOnFlagUntilTimeout              0x08007f35   Thumb Code   148  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART2_IRQHandler                        0x08007fc9   Thumb Code    16  stm32h7xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08007fdd   Thumb Code    18  stm32h7xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x08007ff5   Thumb Code    18  stm32h7xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x0800800d   Thumb Code     2  stm32h7xx_it.o(i.UsageFault_Handler)
    VL53L0X_CheckAndLoadInterruptSettings    0x08008011   Thumb Code   118  vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings)
    VL53L0X_ClearInterruptMask               0x0800808d   Thumb Code    74  vl53l0x_api.o(i.VL53L0X_ClearInterruptMask)
    VL53L0X_DataInit                         0x080080d9   Thumb Code   388  vl53l0x_api.o(i.VL53L0X_DataInit)
    VL53L0X_GetDeviceParameters              0x08008265   Thumb Code   128  vl53l0x_api.o(i.VL53L0X_GetDeviceParameters)
    VL53L0X_GetFractionEnable                0x080082e5   Thumb Code    26  vl53l0x_api.o(i.VL53L0X_GetFractionEnable)
    VL53L0X_GetInterMeasurementPeriodMilliSeconds 0x080082ff   Thumb Code    54  vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds)
    VL53L0X_GetInterruptMaskStatus           0x08008335   Thumb Code    34  vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus)
    VL53L0X_GetInterruptThresholds           0x08008357   Thumb Code    58  vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds)
    VL53L0X_GetLimitCheckEnable              0x08008391   Thumb Code    26  vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable)
    VL53L0X_GetLimitCheckValue               0x080083ad   Thumb Code   126  vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue)
    VL53L0X_GetMeasurementDataReady          0x0800842b   Thumb Code    58  vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady)
    VL53L0X_GetMeasurementTimingBudgetMicroSeconds 0x08008465   Thumb Code     4  vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds)
    VL53L0X_GetOffsetCalibrationDataMicroMeter 0x08008469   Thumb Code     4  vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter)
    VL53L0X_GetRangingMeasurementData        0x0800846d   Thumb Code   320  vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData)
    VL53L0X_GetSequenceStepEnables           0x080085ad   Thumb Code   114  vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables)
    VL53L0X_GetValue                         0x08008621   Thumb Code    24  vl53l0x_i2c_dev.o(i.VL53L0X_GetValue)
    VL53L0X_GetVcselPulsePeriod              0x0800863d   Thumb Code     4  vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod)
    VL53L0X_GetWrapAroundCheckEnable         0x08008641   Thumb Code    44  vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable)
    VL53L0X_GetXTalkCompensationEnable       0x0800866d   Thumb Code     8  vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable)
    VL53L0X_GetXTalkCompensationRateMegaCps  0x08008675   Thumb Code    44  vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps)
    VL53L0X_Init                             0x080086a1   Thumb Code    50  vl53l0x_i2c_dev.o(i.VL53L0X_Init)
    VL53L0X_PerformSingleMeasurement         0x080086d9   Thumb Code    34  vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement)
    VL53L0X_PerformSingleRangingMeasurement  0x080086fb   Thumb Code    52  vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement)
    VL53L0X_PollingDelay                     0x0800872f   Thumb Code    12  vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay)
    VL53L0X_RdByte                           0x0800873b   Thumb Code    18  vl53l0x_i2c_dev.o(i.VL53L0X_RdByte)
    VL53L0X_RdDWord                          0x0800874d   Thumb Code    44  vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord)
    VL53L0X_RdWord                           0x08008779   Thumb Code    32  vl53l0x_i2c_dev.o(i.VL53L0X_RdWord)
    VL53L0X_ReadMulti                        0x08008799   Thumb Code    20  vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti)
    VL53L0X_SetDeviceAddress                 0x080087ad   Thumb Code     8  vl53l0x_api.o(i.VL53L0X_SetDeviceAddress)
    VL53L0X_SetDeviceMode                    0x080087b5   Thumb Code    38  vl53l0x_api.o(i.VL53L0X_SetDeviceMode)
    VL53L0X_SetGpioConfig                    0x080087db   Thumb Code   312  vl53l0x_api.o(i.VL53L0X_SetGpioConfig)
    VL53L0X_SetInterMeasurementPeriodMilliSeconds 0x08008913   Thumb Code    48  vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds)
    VL53L0X_SetLimitCheckEnable              0x08008945   Thumb Code   134  vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable)
    VL53L0X_SetLimitCheckValue               0x080089cb   Thumb Code    80  vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue)
    VL53L0X_SetMeasurementTimingBudgetMicroSeconds 0x08008a1b   Thumb Code     4  vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds)
    VL53L0X_SetPowerMode                     0x08008a1f   Thumb Code    72  vl53l0x_api.o(i.VL53L0X_SetPowerMode)
    VL53L0X_SetSequenceStepEnable            0x08008a67   Thumb Code   176  vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable)
    VL53L0X_StartMeasurement                 0x08008b19   Thumb Code   212  vl53l0x_api.o(i.VL53L0X_StartMeasurement)
    VL53L0X_StaticInit                       0x08008bed   Thumb Code   366  vl53l0x_api.o(i.VL53L0X_StaticInit)
    VL53L0X_UpdateByte                       0x08008d61   Thumb Code    48  vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte)
    VL53L0X_WrByte                           0x08008d91   Thumb Code    14  vl53l0x_i2c_dev.o(i.VL53L0X_WrByte)
    VL53L0X_WrDWord                          0x08008d9f   Thumb Code    40  vl53l0x_i2c_dev.o(i.VL53L0X_WrDWord)
    VL53L0X_WrWord                           0x08008dc7   Thumb Code    28  vl53l0x_i2c_dev.o(i.VL53L0X_WrWord)
    VL53L0X_WriteMulti                       0x08008de3   Thumb Code    20  vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti)
    VL53L0X_calc_dmax                        0x08008df9   Thumb Code   220  vl53l0x_api_core.o(i.VL53L0X_calc_dmax)
    VL53L0X_calc_macro_period_ps             0x08008ed9   Thumb Code    16  vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps)
    VL53L0X_calc_sigma_estimate              0x08008ee9   Thumb Code   556  vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate)
    VL53L0X_calc_timeout_mclks               0x08009121   Thumb Code    34  vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks)
    VL53L0X_calc_timeout_us                  0x08009143   Thumb Code    34  vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us)
    VL53L0X_decode_timeout                   0x08009165   Thumb Code    12  vl53l0x_api_core.o(i.VL53L0X_decode_timeout)
    VL53L0X_decode_vcsel_period              0x08009171   Thumb Code     8  vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period)
    VL53L0X_device_read_strobe               0x08009179   Thumb Code    74  vl53l0x_api_core.o(i.VL53L0X_device_read_strobe)
    VL53L0X_encode_timeout                   0x080091c3   Thumb Code    32  vl53l0x_api_core.o(i.VL53L0X_encode_timeout)
    VL53L0X_get_info_from_device             0x080091e3   Thumb Code  1322  vl53l0x_api_core.o(i.VL53L0X_get_info_from_device)
    VL53L0X_get_measurement_timing_budget_micro_seconds 0x0800970d   Thumb Code   198  vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds)
    VL53L0X_get_offset_calibration_data_micro_meter 0x080097d3   Thumb Code    52  vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter)
    VL53L0X_get_pal_range_status             0x08009809   Thumb Code   482  vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status)
    VL53L0X_get_total_signal_rate            0x080099eb   Thumb Code    28  vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate)
    VL53L0X_get_total_xtalk_rate             0x08009a07   Thumb Code    46  vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate)
    VL53L0X_get_vcsel_pulse_period           0x08009a35   Thumb Code    48  vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period)
    VL53L0X_isqrt                            0x08009a65   Thumb Code    42  vl53l0x_api_core.o(i.VL53L0X_isqrt)
    VL53L0X_load_tuning_settings             0x08009a8f   Thumb Code   164  vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings)
    VL53L0X_measurement_poll_for_completion  0x08009b33   Thumb Code    54  vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion)
    VL53L0X_perform_phase_calibration        0x08009b69   Thumb Code   116  vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration)
    VL53L0X_perform_ref_calibration          0x08009bdd   Thumb Code    64  vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration)
    VL53L0X_perform_ref_spad_management      0x08009c1d   Thumb Code   540  vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management)
    VL53L0X_perform_single_ref_calibration   0x08009e39   Thumb Code    56  vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration)
    VL53L0X_perform_vhv_calibration          0x08009e71   Thumb Code   116  vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration)
    VL53L0X_ref_calibration_io               0x08009ee5   Thumb Code   178  vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io)
    VL53L0X_set_measurement_timing_budget_micro_seconds 0x08009f97   Thumb Code   196  vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds)
    VL53L0X_set_reference_spads              0x0800a05b   Thumb Code   166  vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads)
    __0printf                                0x0800a101   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x0800a101   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x0800a101   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x0800a101   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x0800a101   Thumb Code     0  printfa.o(i.__0printf)
    __0vsprintf                              0x0800a121   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x0800a121   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x0800a121   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x0800a121   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x0800a121   Thumb Code     0  printfa.o(i.__0vsprintf)
    __aeabi_errno_addr                       0x0800a169   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x0800a169   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_atof                            0x0800a171   Thumb Code    44  atof.o(i.__hardfp_atof)
    __read_errno                             0x0800a1a9   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x0800a1b5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800a1c3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800a1c5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800a1d5   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x0800a365   Thumb Code    14  scanf_fp.o(i._is_digit)
    atgm336h_init                            0x0800ab21   Thumb Code    62  atgm336h.o(i.atgm336h_init)
    atgm336h_usart3_irq_handler              0x0800ab89   Thumb Code   480  atgm336h.o(i.atgm336h_usart3_irq_handler)
    clearV831Data                            0x0800ae35   Thumb Code    46  v831.o(i.clearV831Data)
    clrStruct                                0x0800ae69   Thumb Code   102  atgm336h.o(i.clrStruct)
    delay_init                               0x0800aed9   Thumb Code     6  delay.o(i.delay_init)
    delay_ms                                 0x0800aee5   Thumb Code     4  delay.o(i.delay_ms)
    delay_us                                 0x0800aee9   Thumb Code    32  delay.o(i.delay_us)
    dht11_check                              0x0800af0d   Thumb Code    84  dht11.o(i.dht11_check)
    dht11_init                               0x0800af65   Thumb Code    26  dht11.o(i.dht11_init)
    dht11_io_in                              0x0800af81   Thumb Code    34  dht11.o(i.dht11_io_in)
    dht11_io_out                             0x0800afa9   Thumb Code    36  dht11.o(i.dht11_io_out)
    dht11_read_bit                           0x0800afd1   Thumb Code    82  dht11.o(i.dht11_read_bit)
    dht11_read_byte                          0x0800b029   Thumb Code    26  dht11.o(i.dht11_read_byte)
    dht11_read_data                          0x0800b043   Thumb Code    80  dht11.o(i.dht11_read_data)
    dht11_rst                                0x0800b095   Thumb Code    44  dht11.o(i.dht11_rst)
    enable_ref_spads                         0x0800b0c5   Thumb Code   148  vl53l0x_api_calibration.o(i.enable_ref_spads)
    enable_spad_bit                          0x0800b159   Thumb Code    42  vl53l0x_api_calibration.o(i.enable_spad_bit)
    esp8266_receive_msg                      0x0800b185   Thumb Code   112  esp8266.o(i.esp8266_receive_msg)
    fputc                                    0x0800b221   Thumb Code    20  uart_printf.o(i.fputc)
    get_next_good_spad                       0x0800b271   Thumb Code    96  vl53l0x_api_calibration.o(i.get_next_good_spad)
    get_ref_spad_map                         0x0800b2d1   Thumb Code    10  vl53l0x_api_calibration.o(i.get_ref_spad_map)
    get_sequence_step_timeout                0x0800b2db   Thumb Code   244  vl53l0x_api_core.o(i.get_sequence_step_timeout)
    is_aperture                              0x0800b3f1   Thumb Code    18  vl53l0x_api_calibration.o(i.is_aperture)
    main                                     0x0800b409   Thumb Code   142  main.o(i.main)
    oled_write_byte                          0x0800b741   Thumb Code    36  oled.o(i.oled_write_byte)
    parseGpsBuffer                           0x0800b76d   Thumb Code   402  atgm336h.o(i.parseGpsBuffer)
    parseV831Buffer                          0x0800b91d   Thumb Code   538  v831.o(i.parseV831Buffer)
    parse_json_msg                           0x0800bc7d   Thumb Code   108  esp8266.o(i.parse_json_msg)
    perform_ref_signal_measurement           0x0800bd0d   Thumb Code   100  vl53l0x_api_calibration.o(i.perform_ref_signal_measurement)
    printGpsBuffer                           0x0800bd71   Thumb Code   276  atgm336h.o(i.printGpsBuffer)
    printV831Data                            0x0800c02d   Thumb Code    94  v831.o(i.printV831Data)
    sequence_step_enabled                    0x0800c095   Thumb Code    56  vl53l0x_api.o(i.sequence_step_enabled)
    set_led                                  0x0800c0cd   Thumb Code    18  led.o(i.set_led)
    set_ref_spad_map                         0x0800c0e5   Thumb Code    10  vl53l0x_api_calibration.o(i.set_ref_spad_map)
    set_sequence_step_timeout                0x0800c0ef   Thumb Code   260  vl53l0x_api_core.o(i.set_sequence_step_timeout)
    time_slot_start                          0x0800c789   Thumb Code     6  time_handle.o(i.time_slot_start)
    uart2_receiver_clear                     0x0800c795   Thumb Code    22  esp8266.o(i.uart2_receiver_clear)
    uart2_receiver_handle                    0x0800c7b5   Thumb Code    58  esp8266.o(i.uart2_receiver_handle)
    user_init_program                        0x0800c7fd   Thumb Code   118  main_program.o(i.user_init_program)
    user_main_program                        0x0800c8b9   Thumb Code   278  main_program.o(i.user_main_program)
    v831_init                                0x0800ca5d   Thumb Code    54  v831.o(i.v831_init)
    v831_usart6_irq_handler                  0x0800ca9d   Thumb Code   372  v831.o(i.v831_usart6_irq_handler)
    UARTPrescTable                           0x0800ccf0   Data          24  stm32h7xx_hal_uart.o(.constdata)
    D1CorePrescTable                         0x0800cd18   Data          16  system_stm32h7xx.o(.constdata)
    asc2_1206                                0x0800cd28   Data        1140  oled.o(.constdata)
    LOGO_BMP                                 0x0800d19c   Data        1024  oled.o(.constdata)
    __ctype_table                            0x0800d59c   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0800d748   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800d768   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x24000000   Data           1  stm32h7xx_hal.o(.data)
    uwTickPrio                               0x24000004   Data           4  stm32h7xx_hal.o(.data)
    uwTick                                   0x24000008   Data           4  stm32h7xx_hal.o(.data)
    SystemCoreClock                          0x2400000c   Data           4  system_stm32h7xx.o(.data)
    SystemD2Clock                            0x24000010   Data           4  system_stm32h7xx.o(.data)
    receive_start                            0x24000014   Data           1  esp8266.o(.data)
    receive_count                            0x24000016   Data           2  esp8266.o(.data)
    receive_finish                           0x24000018   Data           2  esp8266.o(.data)
    led_status                               0x2400001c   Data           1  main_program.o(.data)
    temp_value                               0x2400001d   Data           1  main_program.o(.data)
    humi_value                               0x2400001e   Data           1  main_program.o(.data)
    Distance                                 0x24000020   Data           2  main_program.o(.data)
    travel_distance                          0x24000024   Data           4  main_program.o(.data)
    index_send_msg                           0x24000030   Data           1  time_handle.o(.data)
    index_led                                0x24000031   Data           1  time_handle.o(.data)
    index_oled                               0x24000034   Data           2  time_handle.o(.data)
    index_dht11                              0x24000036   Data           2  time_handle.o(.data)
    index_gps                                0x24000038   Data           2  time_handle.o(.data)
    index_v831                               0x2400003a   Data           2  time_handle.o(.data)
    index_encoder                            0x2400003c   Data           2  time_handle.o(.data)
    index_vl53l0x                            0x2400003e   Data           2  time_handle.o(.data)
    gps_recv_cplt_flag                       0x24000040   Data           1  atgm336h.o(.data)
    gps_rx_len                               0x24000042   Data           2  atgm336h.o(.data)
    v831_recv_cplt_flag                      0x24000058   Data           1  v831.o(.data)
    v831_rx_len                              0x2400005a   Data           2  v831.o(.data)
    DefaultTuningSettings                    0x24000064   Data         243  vl53l0x_api.o(.data)
    InterruptThresholdSettings               0x24000157   Data         438  vl53l0x_api.o(.data)
    refArrayQuadrants                        0x24000310   Data          16  vl53l0x_api_calibration.o(.data)
    __stdout                                 0x24000324   Data           4  stdout.o(.data)
    hi2c1                                    0x2400032c   Data          84  i2c.o(.bss)
    hi2c2                                    0x24000380   Data          84  i2c.o(.bss)
    htim1                                    0x240003d4   Data          76  tim.o(.bss)
    htim2                                    0x24000420   Data          76  tim.o(.bss)
    htim3                                    0x2400046c   Data          76  tim.o(.bss)
    htim4                                    0x240004b8   Data          76  tim.o(.bss)
    huart1                                   0x24000504   Data         148  usart.o(.bss)
    huart2                                   0x24000598   Data         148  usart.o(.bss)
    huart3                                   0x2400062c   Data         148  usart.o(.bss)
    huart6                                   0x240006c0   Data         148  usart.o(.bss)
    hdma_usart3_rx                           0x24000754   Data         120  usart.o(.bss)
    hdma_usart6_rx                           0x240007cc   Data         120  usart.o(.bss)
    receive_buf                              0x24000844   Data         512  esp8266.o(.bss)
    g_LatAndLongData                         0x24000ec4   Data          12  atgm336h.o(.bss)
    gps_dma_rx_buffer                        0x24000ed0   Data         256  atgm336h.o(.bss)
    Save_Data                                0x24000fd0   Data         180  atgm336h.o(.bss)
    g_V831DetectionData                      0x24001084   Data         324  v831.o(.bss)
    v831_dma_rx_buffer                       0x240011c8   Data         512  v831.o(.bss)
    V831_Data                                0x240013c8   Data         844  v831.o(.bss)
    __initial_sp                             0x24001c90   Data           0  startup_stm32h723xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080002cd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000da94, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0000da10])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000d768, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000002cc   Data   RO            3    RESET               startup_stm32h723xx.o
    0x080002cc   0x080002cc   0x00000000   Code   RO         6558  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080002cc   0x080002cc   0x00000004   Code   RO         6856    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080002d0   0x080002d0   0x00000004   Code   RO         6859    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080002d4   0x080002d4   0x00000000   Code   RO         6861    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080002d4   0x080002d4   0x00000000   Code   RO         6863    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080002d4   0x080002d4   0x00000008   Code   RO         6864    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080002dc   0x080002dc   0x00000004   Code   RO         6871    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO         6866    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO         6868    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080002e0   0x080002e0   0x00000004   Code   RO         6857    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080002e4   0x080002e4   0x0000002c   Code   RO            4    .text               startup_stm32h723xx.o
    0x08000310   0x08000310   0x00000062   Code   RO         6561    .text               mc_w.l(uldiv.o)
    0x08000372   0x08000372   0x00000024   Code   RO         6565    .text               mc_w.l(memcpya.o)
    0x08000396   0x08000396   0x00000024   Code   RO         6567    .text               mc_w.l(memseta.o)
    0x080003ba   0x080003ba   0x00000024   Code   RO         6569    .text               mc_w.l(strstr.o)
    0x080003de   0x080003de   0x00000018   Code   RO         6571    .text               mc_w.l(strncpy.o)
    0x080003f6   0x080003f6   0x00000014   Code   RO         6573    .text               mc_w.l(strchr.o)
    0x0800040a   0x0800040a   0x0000000e   Code   RO         6575    .text               mc_w.l(strlen.o)
    0x08000418   0x08000418   0x00000012   Code   RO         6577    .text               mc_w.l(strcpy.o)
    0x0800042a   0x0800042a   0x00000002   PAD
    0x0800042c   0x0800042c   0x00000044   Code   RO         6579    .text               mc_w.l(strtok.o)
    0x08000470   0x08000470   0x00000038   Code   RO         6848    .text               mc_w.l(__0sscanf.o)
    0x080004a8   0x080004a8   0x0000014c   Code   RO         6850    .text               mc_w.l(_scanf_int.o)
    0x080005f4   0x080005f4   0x000000e0   Code   RO         6852    .text               mc_w.l(_scanf_str.o)
    0x080006d4   0x080006d4   0x0000001a   Code   RO         6854    .text               mc_w.l(atoi.o)
    0x080006ee   0x080006ee   0x0000002c   Code   RO         6873    .text               mc_w.l(uidiv.o)
    0x0800071a   0x0800071a   0x0000001e   Code   RO         6875    .text               mc_w.l(llshl.o)
    0x08000738   0x08000738   0x00000020   Code   RO         6877    .text               mc_w.l(llushr.o)
    0x08000758   0x08000758   0x0000001c   Code   RO         6886    .text               mc_w.l(_chval.o)
    0x08000774   0x08000774   0x00000028   Code   RO         6888    .text               mc_w.l(scanf_char.o)
    0x0800079c   0x0800079c   0x00000040   Code   RO         6890    .text               mc_w.l(_sgetc.o)
    0x080007dc   0x080007dc   0x0000009c   Code   RO         6892    .text               mc_w.l(strtod.o)
    0x08000878   0x08000878   0x00000070   Code   RO         6894    .text               mc_w.l(strtol.o)
    0x080008e8   0x080008e8   0x00000000   Code   RO         6896    .text               mc_w.l(iusefp.o)
    0x080008e8   0x080008e8   0x0000014e   Code   RO         6897    .text               mf_w.l(dadd.o)
    0x08000a36   0x08000a36   0x000000e4   Code   RO         6899    .text               mf_w.l(dmul.o)
    0x08000b1a   0x08000b1a   0x000000de   Code   RO         6901    .text               mf_w.l(ddiv.o)
    0x08000bf8   0x08000bf8   0x00000030   Code   RO         6903    .text               mf_w.l(dfixul.o)
    0x08000c28   0x08000c28   0x00000030   Code   RO         6905    .text               mf_w.l(cdrcmple.o)
    0x08000c58   0x08000c58   0x00000024   Code   RO         6907    .text               mc_w.l(init.o)
    0x08000c7c   0x08000c7c   0x00000024   Code   RO         6909    .text               mc_w.l(llsshr.o)
    0x08000ca0   0x08000ca0   0x00000008   Code   RO         6913    .text               mc_w.l(ctype_o.o)
    0x08000ca8   0x08000ca8   0x00000012   Code   RO         6935    .text               mc_w.l(isspace_o.o)
    0x08000cba   0x08000cba   0x00000002   PAD
    0x08000cbc   0x08000cbc   0x00000330   Code   RO         6941    .text               mc_w.l(_scanf.o)
    0x08000fec   0x08000fec   0x00000360   Code   RO         6943    .text               mc_w.l(scanf_fp.o)
    0x0800134c   0x0800134c   0x0000009e   Code   RO         6947    .text               mc_w.l(_strtoul.o)
    0x080013ea   0x080013ea   0x000000ba   Code   RO         6949    .text               mf_w.l(depilogue.o)
    0x080014a4   0x080014a4   0x00000018   Code   RO         6954    .text               mf_w.l(dfltul.o)
    0x080014bc   0x080014bc   0x00000038   Code   RO         6956    .text               mf_w.l(d2f.o)
    0x080014f4   0x080014f4   0x0000006e   Code   RO         6958    .text               mf_w.l(fepilogue.o)
    0x08001562   0x08001562   0x0000005e   Code   RO         6968    .text               mc_w.l(__dclz77c.o)
    0x080015c0   0x080015c0   0x00000002   Code   RO          439    i.BusFault_Handler  stm32h7xx_it.o
    0x080015c2   0x080015c2   0x00000002   PAD
    0x080015c4   0x080015c4   0x0000000c   Code   RO          440    i.DMA1_Stream0_IRQHandler  stm32h7xx_it.o
    0x080015d0   0x080015d0   0x0000000c   Code   RO          441    i.DMA1_Stream1_IRQHandler  stm32h7xx_it.o
    0x080015dc   0x080015dc   0x000000b4   Code   RO         1351    i.DMA_CalcBaseAndBitshift  stm32h7xx_hal_dma.o
    0x08001690   0x08001690   0x0000009c   Code   RO         1352    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32h7xx_hal_dma.o
    0x0800172c   0x0800172c   0x00000074   Code   RO         1353    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32h7xx_hal_dma.o
    0x080017a0   0x080017a0   0x00000054   Code   RO         1354    i.DMA_CheckFifoParam  stm32h7xx_hal_dma.o
    0x080017f4   0x080017f4   0x00000220   Code   RO         1355    i.DMA_SetConfig     stm32h7xx_hal_dma.o
    0x08001a14   0x08001a14   0x00000002   Code   RO          442    i.DebugMon_Handler  stm32h7xx_it.o
    0x08001a16   0x08001a16   0x00000002   PAD
    0x08001a18   0x08001a18   0x0000001c   Code   RO         5270    i.Encoder_GetDistanceMM  motor.o
    0x08001a34   0x08001a34   0x00000028   Code   RO         5271    i.Encoder_Init      motor.o
    0x08001a5c   0x08001a5c   0x00000034   Code   RO         5273    i.Encoder_Update    motor.o
    0x08001a90   0x08001a90   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08001a94   0x08001a94   0x0000001c   Code   RO         4549    i.ExitRun0Mode      system_stm32h7xx.o
    0x08001ab0   0x08001ab0   0x00000360   Code   RO         1356    i.HAL_DMA_Abort     stm32h7xx_hal_dma.o
    0x08001e10   0x08001e10   0x00000284   Code   RO         1357    i.HAL_DMA_Abort_IT  stm32h7xx_hal_dma.o
    0x08002094   0x08002094   0x00000004   Code   RO         1359    i.HAL_DMA_GetError  stm32h7xx_hal_dma.o
    0x08002098   0x08002098   0x000006e8   Code   RO         1361    i.HAL_DMA_IRQHandler  stm32h7xx_hal_dma.o
    0x08002780   0x08002780   0x000003bc   Code   RO         1362    i.HAL_DMA_Init      stm32h7xx_hal_dma.o
    0x08002b3c   0x08002b3c   0x0000029c   Code   RO         1366    i.HAL_DMA_Start_IT  stm32h7xx_hal_dma.o
    0x08002dd8   0x08002dd8   0x00000024   Code   RO         2053    i.HAL_Delay         stm32h7xx_hal.o
    0x08002dfc   0x08002dfc   0x0000021c   Code   RO         1204    i.HAL_GPIO_Init     stm32h7xx_hal_gpio.o
    0x08003018   0x08003018   0x0000000a   Code   RO         1206    i.HAL_GPIO_ReadPin  stm32h7xx_hal_gpio.o
    0x08003022   0x08003022   0x0000000a   Code   RO         1208    i.HAL_GPIO_WritePin  stm32h7xx_hal_gpio.o
    0x0800302c   0x0800302c   0x0000000c   Code   RO         2069    i.HAL_GetTick       stm32h7xx_hal.o
    0x08003038   0x08003038   0x00000058   Code   RO         2866    i.HAL_I2CEx_ConfigAnalogFilter  stm32h7xx_hal_i2c_ex.o
    0x08003090   0x08003090   0x00000054   Code   RO         2867    i.HAL_I2CEx_ConfigDigitalFilter  stm32h7xx_hal_i2c_ex.o
    0x080030e4   0x080030e4   0x000000c0   Code   RO         2405    i.HAL_I2C_Init      stm32h7xx_hal_i2c.o
    0x080031a4   0x080031a4   0x0000014c   Code   RO         2418    i.HAL_I2C_Master_Transmit  stm32h7xx_hal_i2c.o
    0x080032f0   0x080032f0   0x00000164   Code   RO         2423    i.HAL_I2C_Mem_Read  stm32h7xx_hal_i2c.o
    0x08003454   0x08003454   0x00000158   Code   RO         2426    i.HAL_I2C_Mem_Write  stm32h7xx_hal_i2c.o
    0x080035ac   0x080035ac   0x000000f4   Code   RO          254    i.HAL_I2C_MspInit   i2c.o
    0x080036a0   0x080036a0   0x00000010   Code   RO         2075    i.HAL_IncTick       stm32h7xx_hal.o
    0x080036b0   0x080036b0   0x0000005c   Code   RO         2076    i.HAL_Init          stm32h7xx_hal.o
    0x0800370c   0x0800370c   0x00000040   Code   RO         2077    i.HAL_InitTick      stm32h7xx_hal.o
    0x0800374c   0x0800374c   0x0000005c   Code   RO          579    i.HAL_MPU_ConfigRegion  stm32h7xx_hal_cortex.o
    0x080037a8   0x080037a8   0x0000001c   Code   RO          580    i.HAL_MPU_Disable   stm32h7xx_hal_cortex.o
    0x080037c4   0x080037c4   0x00000024   Code   RO          582    i.HAL_MPU_Enable    stm32h7xx_hal_cortex.o
    0x080037e8   0x080037e8   0x0000001c   Code   RO          554    i.HAL_MspInit       stm32h7xx_hal_msp.o
    0x08003804   0x08003804   0x0000001a   Code   RO          586    i.HAL_NVIC_EnableIRQ  stm32h7xx_hal_cortex.o
    0x0800381e   0x0800381e   0x00000002   PAD
    0x08003820   0x08003820   0x00000040   Code   RO          592    i.HAL_NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08003860   0x08003860   0x00000024   Code   RO          593    i.HAL_NVIC_SetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x08003884   0x08003884   0x00000054   Code   RO         1805    i.HAL_PWREx_ConfigSupply  stm32h7xx_hal_pwr_ex.o
    0x080038d8   0x080038d8   0x00000024   Code   RO          847    i.HAL_RCCEx_GetD3PCLK1Freq  stm32h7xx_hal_rcc_ex.o
    0x080038fc   0x080038fc   0x00000144   Code   RO          849    i.HAL_RCCEx_GetPLL2ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08003a40   0x08003a40   0x00000144   Code   RO          850    i.HAL_RCCEx_GetPLL3ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08003b84   0x08003b84   0x0000093a   Code   RO          856    i.HAL_RCCEx_PeriphCLKConfig  stm32h7xx_hal_rcc_ex.o
    0x080044be   0x080044be   0x00000002   PAD
    0x080044c0   0x080044c0   0x0000025c   Code   RO          732    i.HAL_RCC_ClockConfig  stm32h7xx_hal_rcc.o
    0x0800471c   0x0800471c   0x00000044   Code   RO          737    i.HAL_RCC_GetHCLKFreq  stm32h7xx_hal_rcc.o
    0x08004760   0x08004760   0x00000024   Code   RO          739    i.HAL_RCC_GetPCLK1Freq  stm32h7xx_hal_rcc.o
    0x08004784   0x08004784   0x00000024   Code   RO          740    i.HAL_RCC_GetPCLK2Freq  stm32h7xx_hal_rcc.o
    0x080047a8   0x080047a8   0x00000138   Code   RO          741    i.HAL_RCC_GetSysClockFreq  stm32h7xx_hal_rcc.o
    0x080048e0   0x080048e0   0x00000526   Code   RO          744    i.HAL_RCC_OscConfig  stm32h7xx_hal_rcc.o
    0x08004e06   0x08004e06   0x00000026   Code   RO          597    i.HAL_SYSTICK_Config  stm32h7xx_hal_cortex.o
    0x08004e2c   0x08004e2c   0x00000002   Code   RO         3708    i.HAL_TIMEx_Break2Callback  stm32h7xx_hal_tim_ex.o
    0x08004e2e   0x08004e2e   0x00000002   Code   RO         3709    i.HAL_TIMEx_BreakCallback  stm32h7xx_hal_tim_ex.o
    0x08004e30   0x08004e30   0x00000002   Code   RO         3710    i.HAL_TIMEx_CommutCallback  stm32h7xx_hal_tim_ex.o
    0x08004e32   0x08004e32   0x00000002   PAD
    0x08004e34   0x08004e34   0x000000b8   Code   RO         3731    i.HAL_TIMEx_MasterConfigSynchronization  stm32h7xx_hal_tim_ex.o
    0x08004eec   0x08004eec   0x00000062   Code   RO         2994    i.HAL_TIM_Base_Init  stm32h7xx_hal_tim.o
    0x08004f4e   0x08004f4e   0x00000002   PAD
    0x08004f50   0x08004f50   0x00000070   Code   RO          302    i.HAL_TIM_Base_MspInit  tim.o
    0x08004fc0   0x08004fc0   0x00000094   Code   RO         2997    i.HAL_TIM_Base_Start  stm32h7xx_hal_tim.o
    0x08005054   0x08005054   0x000000a0   Code   RO         2999    i.HAL_TIM_Base_Start_IT  stm32h7xx_hal_tim.o
    0x080050f4   0x080050f4   0x000000fc   Code   RO         3003    i.HAL_TIM_ConfigClockSource  stm32h7xx_hal_tim.o
    0x080051f0   0x080051f0   0x000000bc   Code   RO         3015    i.HAL_TIM_Encoder_Init  stm32h7xx_hal_tim.o
    0x080052ac   0x080052ac   0x00000064   Code   RO          304    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08005310   0x08005310   0x0000008e   Code   RO         3018    i.HAL_TIM_Encoder_Start  stm32h7xx_hal_tim.o
    0x0800539e   0x0800539e   0x00000002   Code   RO         3028    i.HAL_TIM_IC_CaptureCallback  stm32h7xx_hal_tim.o
    0x080053a0   0x080053a0   0x00000148   Code   RO         3042    i.HAL_TIM_IRQHandler  stm32h7xx_hal_tim.o
    0x080054e8   0x080054e8   0x00000050   Code   RO          305    i.HAL_TIM_MspPostInit  tim.o
    0x08005538   0x08005538   0x00000002   Code   RO         3045    i.HAL_TIM_OC_DelayElapsedCallback  stm32h7xx_hal_tim.o
    0x0800553a   0x0800553a   0x00000124   Code   RO         3066    i.HAL_TIM_PWM_ConfigChannel  stm32h7xx_hal_tim.o
    0x0800565e   0x0800565e   0x00000062   Code   RO         3069    i.HAL_TIM_PWM_Init  stm32h7xx_hal_tim.o
    0x080056c0   0x080056c0   0x00000002   Code   RO         3071    i.HAL_TIM_PWM_MspInit  stm32h7xx_hal_tim.o
    0x080056c2   0x080056c2   0x00000002   Code   RO         3072    i.HAL_TIM_PWM_PulseFinishedCallback  stm32h7xx_hal_tim.o
    0x080056c4   0x080056c4   0x00000124   Code   RO         3074    i.HAL_TIM_PWM_Start  stm32h7xx_hal_tim.o
    0x080057e8   0x080057e8   0x0000006c   Code   RO         5090    i.HAL_TIM_PeriodElapsedCallback  time_handle.o
    0x08005854   0x08005854   0x00000002   Code   RO         3085    i.HAL_TIM_TriggerCallback  stm32h7xx_hal_tim.o
    0x08005856   0x08005856   0x00000040   Code   RO         4431    i.HAL_UARTEx_DisableFifoMode  stm32h7xx_hal_uart_ex.o
    0x08005896   0x08005896   0x00000002   Code   RO         4020    i.HAL_UARTEx_RxEventCallback  stm32h7xx_hal_uart.o
    0x08005898   0x08005898   0x00000002   Code   RO         4439    i.HAL_UARTEx_RxFifoFullCallback  stm32h7xx_hal_uart_ex.o
    0x0800589a   0x0800589a   0x0000004c   Code   RO         4440    i.HAL_UARTEx_SetRxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x080058e6   0x080058e6   0x0000004c   Code   RO         4441    i.HAL_UARTEx_SetTxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x08005932   0x08005932   0x00000002   Code   RO         4443    i.HAL_UARTEx_TxFifoEmptyCallback  stm32h7xx_hal_uart_ex.o
    0x08005934   0x08005934   0x00000002   Code   RO         4444    i.HAL_UARTEx_WakeupCallback  stm32h7xx_hal_uart_ex.o
    0x08005936   0x08005936   0x00000092   Code   RO         4032    i.HAL_UART_DMAStop  stm32h7xx_hal_uart.o
    0x080059c8   0x080059c8   0x00000002   Code   RO         4036    i.HAL_UART_ErrorCallback  stm32h7xx_hal_uart.o
    0x080059ca   0x080059ca   0x00000002   PAD
    0x080059cc   0x080059cc   0x0000039c   Code   RO         4039    i.HAL_UART_IRQHandler  stm32h7xx_hal_uart.o
    0x08005d68   0x08005d68   0x0000006a   Code   RO         4040    i.HAL_UART_Init     stm32h7xx_hal_uart.o
    0x08005dd2   0x08005dd2   0x00000002   PAD
    0x08005dd4   0x08005dd4   0x00000268   Code   RO          380    i.HAL_UART_MspInit  usart.o
    0x0800603c   0x0800603c   0x000000e8   Code   RO         4043    i.HAL_UART_Receive  stm32h7xx_hal_uart.o
    0x08006124   0x08006124   0x00000048   Code   RO         4044    i.HAL_UART_Receive_DMA  stm32h7xx_hal_uart.o
    0x0800616c   0x0800616c   0x00000002   Code   RO           14    i.HAL_UART_RxCpltCallback  main.o
    0x0800616e   0x0800616e   0x00000002   Code   RO         4048    i.HAL_UART_RxHalfCpltCallback  stm32h7xx_hal_uart.o
    0x08006170   0x08006170   0x000000b0   Code   RO         4049    i.HAL_UART_Transmit  stm32h7xx_hal_uart.o
    0x08006220   0x08006220   0x00000002   Code   RO         4052    i.HAL_UART_TxCpltCallback  stm32h7xx_hal_uart.o
    0x08006222   0x08006222   0x00000002   Code   RO          443    i.HardFault_Handler  stm32h7xx_it.o
    0x08006224   0x08006224   0x00000022   Code   RO         2452    i.I2C_Flush_TXDR    stm32h7xx_hal_i2c.o
    0x08006246   0x08006246   0x00000002   PAD
    0x08006248   0x08006248   0x00000110   Code   RO         2460    i.I2C_IsErrorOccurred  stm32h7xx_hal_i2c.o
    0x08006358   0x08006358   0x00000064   Code   RO         2465    i.I2C_RequestMemoryRead  stm32h7xx_hal_i2c.o
    0x080063bc   0x080063bc   0x00000064   Code   RO         2466    i.I2C_RequestMemoryWrite  stm32h7xx_hal_i2c.o
    0x08006420   0x08006420   0x00000030   Code   RO         2469    i.I2C_TransferConfig  stm32h7xx_hal_i2c.o
    0x08006450   0x08006450   0x0000007c   Code   RO         2471    i.I2C_WaitOnFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x080064cc   0x080064cc   0x00000058   Code   RO         2473    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x08006524   0x08006524   0x0000005c   Code   RO         2474    i.I2C_WaitOnTXISFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x08006580   0x08006580   0x00000064   Code   RO         4590    i.JSON_SearchConst  core_json.o
    0x080065e4   0x080065e4   0x00000012   Code   RO         4591    i.JSON_SearchT      core_json.o
    0x080065f6   0x080065f6   0x0000005e   Code   RO         4592    i.JSON_Validate     core_json.o
    0x08006654   0x08006654   0x0000003c   Code   RO          229    i.MX_DMA_Init       dma.o
    0x08006690   0x08006690   0x000000c8   Code   RO          205    i.MX_GPIO_Init      gpio.o
    0x08006758   0x08006758   0x00000058   Code   RO          255    i.MX_I2C1_Init      i2c.o
    0x080067b0   0x080067b0   0x00000058   Code   RO          256    i.MX_I2C2_Init      i2c.o
    0x08006808   0x08006808   0x0000006c   Code   RO          306    i.MX_TIM1_Init      tim.o
    0x08006874   0x08006874   0x00000064   Code   RO          307    i.MX_TIM2_Init      tim.o
    0x080068d8   0x080068d8   0x00000068   Code   RO          308    i.MX_TIM3_Init      tim.o
    0x08006940   0x08006940   0x0000009c   Code   RO          309    i.MX_TIM4_Init      tim.o
    0x080069dc   0x080069dc   0x00000068   Code   RO          381    i.MX_USART1_UART_Init  usart.o
    0x08006a44   0x08006a44   0x00000068   Code   RO          382    i.MX_USART2_UART_Init  usart.o
    0x08006aac   0x08006aac   0x00000068   Code   RO          383    i.MX_USART3_UART_Init  usart.o
    0x08006b14   0x08006b14   0x00000068   Code   RO          384    i.MX_USART6_UART_Init  usart.o
    0x08006b7c   0x08006b7c   0x00000002   Code   RO          444    i.MemManage_Handler  stm32h7xx_it.o
    0x08006b7e   0x08006b7e   0x00000002   PAD
    0x08006b80   0x08006b80   0x0000003c   Code   RO         5274    i.Motor_Control     motor.o
    0x08006bbc   0x08006bbc   0x00000002   Code   RO         5275    i.Motor_Init        motor.o
    0x08006bbe   0x08006bbe   0x00000002   Code   RO          445    i.NMI_Handler       stm32h7xx_it.o
    0x08006bc0   0x08006bc0   0x00000048   Code   RO         4983    i.OLED_draw_point   oled.o
    0x08006c08   0x08006c08   0x000000e6   Code   RO         4984    i.OLED_init         oled.o
    0x08006cee   0x08006cee   0x00000002   PAD
    0x08006cf0   0x08006cf0   0x00000040   Code   RO         4985    i.OLED_operate_gram  oled.o
    0x08006d30   0x08006d30   0x00000044   Code   RO         4986    i.OLED_printf       oled.o
    0x08006d74   0x08006d74   0x00000034   Code   RO         4987    i.OLED_refresh_gram  oled.o
    0x08006da8   0x08006da8   0x00000028   Code   RO         4988    i.OLED_set_pos      oled.o
    0x08006dd0   0x08006dd0   0x00000078   Code   RO         4989    i.OLED_show_char    oled.o
    0x08006e48   0x08006e48   0x00000036   Code   RO         4990    i.OLED_show_string  oled.o
    0x08006e7e   0x08006e7e   0x00000002   Code   RO          446    i.PendSV_Handler    stm32h7xx_it.o
    0x08006e80   0x08006e80   0x00000120   Code   RO          859    i.RCCEx_PLL2_Config  stm32h7xx_hal_rcc_ex.o
    0x08006fa0   0x08006fa0   0x00000120   Code   RO          860    i.RCCEx_PLL3_Config  stm32h7xx_hal_rcc_ex.o
    0x080070c0   0x080070c0   0x00000014   Code   RO         5331    i.SERVO_Clamp       servo.o
    0x080070d4   0x080070d4   0x0000001c   Code   RO         5332    i.SERVO_Init        servo.o
    0x080070f0   0x080070f0   0x00000014   Code   RO         5333    i.SERVO_Release     servo.o
    0x08007104   0x08007104   0x00000024   Code   RO         5152    i.ST_IICreadBytes   st_i2c.o
    0x08007128   0x08007128   0x0000000c   Code   RO         5154    i.ST_IICwriteByte   st_i2c.o
    0x08007134   0x08007134   0x00000024   Code   RO         5155    i.ST_IICwriteBytes  st_i2c.o
    0x08007158   0x08007158   0x00000002   Code   RO          447    i.SVC_Handler       stm32h7xx_it.o
    0x0800715a   0x0800715a   0x00000004   Code   RO          448    i.SysTick_Handler   stm32h7xx_it.o
    0x0800715e   0x0800715e   0x00000002   PAD
    0x08007160   0x08007160   0x00000094   Code   RO           15    i.SystemClock_Config  main.o
    0x080071f4   0x080071f4   0x000000d8   Code   RO         4551    i.SystemInit        system_stm32h7xx.o
    0x080072cc   0x080072cc   0x0000000c   Code   RO          449    i.TIM2_IRQHandler   stm32h7xx_it.o
    0x080072d8   0x080072d8   0x000000e0   Code   RO         3087    i.TIM_Base_SetConfig  stm32h7xx_hal_tim.o
    0x080073b8   0x080073b8   0x0000001a   Code   RO         3088    i.TIM_CCxChannelCmd  stm32h7xx_hal_tim.o
    0x080073d2   0x080073d2   0x00000012   Code   RO         3098    i.TIM_ETR_SetConfig  stm32h7xx_hal_tim.o
    0x080073e4   0x080073e4   0x00000014   Code   RO         3099    i.TIM_ITRx_SetConfig  stm32h7xx_hal_tim.o
    0x080073f8   0x080073f8   0x000000a4   Code   RO         3100    i.TIM_OC1_SetConfig  stm32h7xx_hal_tim.o
    0x0800749c   0x0800749c   0x00000098   Code   RO         3101    i.TIM_OC2_SetConfig  stm32h7xx_hal_tim.o
    0x08007534   0x08007534   0x00000098   Code   RO         3102    i.TIM_OC3_SetConfig  stm32h7xx_hal_tim.o
    0x080075cc   0x080075cc   0x00000078   Code   RO         3103    i.TIM_OC4_SetConfig  stm32h7xx_hal_tim.o
    0x08007644   0x08007644   0x00000074   Code   RO         3104    i.TIM_OC5_SetConfig  stm32h7xx_hal_tim.o
    0x080076b8   0x080076b8   0x00000074   Code   RO         3105    i.TIM_OC6_SetConfig  stm32h7xx_hal_tim.o
    0x0800772c   0x0800772c   0x00000022   Code   RO         3107    i.TIM_TI1_ConfigInputStage  stm32h7xx_hal_tim.o
    0x0800774e   0x0800774e   0x00000024   Code   RO         3109    i.TIM_TI2_ConfigInputStage  stm32h7xx_hal_tim.o
    0x08007772   0x08007772   0x00000002   PAD
    0x08007774   0x08007774   0x00000044   Code   RO         4445    i.UARTEx_SetNbDataToProcess  stm32h7xx_hal_uart_ex.o
    0x080077b8   0x080077b8   0x000000c8   Code   RO         4054    i.UART_AdvFeatureConfig  stm32h7xx_hal_uart.o
    0x08007880   0x08007880   0x000000aa   Code   RO         4055    i.UART_CheckIdleState  stm32h7xx_hal_uart.o
    0x0800792a   0x0800792a   0x00000010   Code   RO         4056    i.UART_DMAAbortOnError  stm32h7xx_hal_uart.o
    0x0800793a   0x0800793a   0x0000004e   Code   RO         4057    i.UART_DMAError     stm32h7xx_hal_uart.o
    0x08007988   0x08007988   0x00000082   Code   RO         4058    i.UART_DMAReceiveCplt  stm32h7xx_hal_uart.o
    0x08007a0a   0x08007a0a   0x00000020   Code   RO         4060    i.UART_DMARxHalfCplt  stm32h7xx_hal_uart.o
    0x08007a2a   0x08007a2a   0x00000002   PAD
    0x08007a2c   0x08007a2c   0x00000054   Code   RO         4066    i.UART_EndRxTransfer  stm32h7xx_hal_uart.o
    0x08007a80   0x08007a80   0x0000002e   Code   RO         4067    i.UART_EndTxTransfer  stm32h7xx_hal_uart.o
    0x08007aae   0x08007aae   0x00000002   PAD
    0x08007ab0   0x08007ab0   0x000003dc   Code   RO         4072    i.UART_SetConfig    stm32h7xx_hal_uart.o
    0x08007e8c   0x08007e8c   0x000000a8   Code   RO         4073    i.UART_Start_Receive_DMA  stm32h7xx_hal_uart.o
    0x08007f34   0x08007f34   0x00000094   Code   RO         4079    i.UART_WaitOnFlagUntilTimeout  stm32h7xx_hal_uart.o
    0x08007fc8   0x08007fc8   0x00000014   Code   RO          450    i.USART2_IRQHandler  stm32h7xx_it.o
    0x08007fdc   0x08007fdc   0x00000018   Code   RO          451    i.USART3_IRQHandler  stm32h7xx_it.o
    0x08007ff4   0x08007ff4   0x00000018   Code   RO          452    i.USART6_IRQHandler  stm32h7xx_it.o
    0x0800800c   0x0800800c   0x00000002   Code   RO          453    i.UsageFault_Handler  stm32h7xx_it.o
    0x0800800e   0x0800800e   0x00000002   PAD
    0x08008010   0x08008010   0x0000007c   Code   RO         5595    i.VL53L0X_CheckAndLoadInterruptSettings  vl53l0x_api.o
    0x0800808c   0x0800808c   0x0000004a   Code   RO         5596    i.VL53L0X_ClearInterruptMask  vl53l0x_api.o
    0x080080d6   0x080080d6   0x00000002   PAD
    0x080080d8   0x080080d8   0x0000018c   Code   RO         5597    i.VL53L0X_DataInit  vl53l0x_api.o
    0x08008264   0x08008264   0x00000080   Code   RO         5603    i.VL53L0X_GetDeviceParameters  vl53l0x_api.o
    0x080082e4   0x080082e4   0x0000001a   Code   RO         5605    i.VL53L0X_GetFractionEnable  vl53l0x_api.o
    0x080082fe   0x080082fe   0x00000036   Code   RO         5609    i.VL53L0X_GetInterMeasurementPeriodMilliSeconds  vl53l0x_api.o
    0x08008334   0x08008334   0x00000022   Code   RO         5610    i.VL53L0X_GetInterruptMaskStatus  vl53l0x_api.o
    0x08008356   0x08008356   0x0000003a   Code   RO         5611    i.VL53L0X_GetInterruptThresholds  vl53l0x_api.o
    0x08008390   0x08008390   0x0000001a   Code   RO         5613    i.VL53L0X_GetLimitCheckEnable  vl53l0x_api.o
    0x080083aa   0x080083aa   0x00000002   PAD
    0x080083ac   0x080083ac   0x0000007e   Code   RO         5616    i.VL53L0X_GetLimitCheckValue  vl53l0x_api.o
    0x0800842a   0x0800842a   0x0000003a   Code   RO         5619    i.VL53L0X_GetMeasurementDataReady  vl53l0x_api.o
    0x08008464   0x08008464   0x00000004   Code   RO         5621    i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds  vl53l0x_api.o
    0x08008468   0x08008468   0x00000004   Code   RO         5625    i.VL53L0X_GetOffsetCalibrationDataMicroMeter  vl53l0x_api.o
    0x0800846c   0x0800846c   0x00000140   Code   RO         5633    i.VL53L0X_GetRangingMeasurementData  vl53l0x_api.o
    0x080085ac   0x080085ac   0x00000072   Code   RO         5637    i.VL53L0X_GetSequenceStepEnables  vl53l0x_api.o
    0x0800861e   0x0800861e   0x00000002   PAD
    0x08008620   0x08008620   0x0000001c   Code   RO         5473    i.VL53L0X_GetValue  vl53l0x_i2c_dev.o
    0x0800863c   0x0800863c   0x00000004   Code   RO         5646    i.VL53L0X_GetVcselPulsePeriod  vl53l0x_api.o
    0x08008640   0x08008640   0x0000002c   Code   RO         5648    i.VL53L0X_GetWrapAroundCheckEnable  vl53l0x_api.o
    0x0800866c   0x0800866c   0x00000008   Code   RO         5649    i.VL53L0X_GetXTalkCompensationEnable  vl53l0x_api.o
    0x08008674   0x08008674   0x0000002c   Code   RO         5650    i.VL53L0X_GetXTalkCompensationRateMegaCps  vl53l0x_api.o
    0x080086a0   0x080086a0   0x00000038   Code   RO         5474    i.VL53L0X_Init      vl53l0x_i2c_dev.o
    0x080086d8   0x080086d8   0x00000022   Code   RO         5655    i.VL53L0X_PerformSingleMeasurement  vl53l0x_api.o
    0x080086fa   0x080086fa   0x00000034   Code   RO         5656    i.VL53L0X_PerformSingleRangingMeasurement  vl53l0x_api.o
    0x0800872e   0x0800872e   0x0000000c   Code   RO         5475    i.VL53L0X_PollingDelay  vl53l0x_i2c_dev.o
    0x0800873a   0x0800873a   0x00000012   Code   RO         5476    i.VL53L0X_RdByte    vl53l0x_i2c_dev.o
    0x0800874c   0x0800874c   0x0000002c   Code   RO         5477    i.VL53L0X_RdDWord   vl53l0x_i2c_dev.o
    0x08008778   0x08008778   0x00000020   Code   RO         5478    i.VL53L0X_RdWord    vl53l0x_i2c_dev.o
    0x08008798   0x08008798   0x00000014   Code   RO         5479    i.VL53L0X_ReadMulti  vl53l0x_i2c_dev.o
    0x080087ac   0x080087ac   0x00000008   Code   RO         5660    i.VL53L0X_SetDeviceAddress  vl53l0x_api.o
    0x080087b4   0x080087b4   0x00000026   Code   RO         5661    i.VL53L0X_SetDeviceMode  vl53l0x_api.o
    0x080087da   0x080087da   0x00000138   Code   RO         5664    i.VL53L0X_SetGpioConfig  vl53l0x_api.o
    0x08008912   0x08008912   0x00000030   Code   RO         5667    i.VL53L0X_SetInterMeasurementPeriodMilliSeconds  vl53l0x_api.o
    0x08008942   0x08008942   0x00000002   PAD
    0x08008944   0x08008944   0x00000086   Code   RO         5669    i.VL53L0X_SetLimitCheckEnable  vl53l0x_api.o
    0x080089ca   0x080089ca   0x00000050   Code   RO         5670    i.VL53L0X_SetLimitCheckValue  vl53l0x_api.o
    0x08008a1a   0x08008a1a   0x00000004   Code   RO         5672    i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds  vl53l0x_api.o
    0x08008a1e   0x08008a1e   0x00000048   Code   RO         5675    i.VL53L0X_SetPowerMode  vl53l0x_api.o
    0x08008a66   0x08008a66   0x000000b0   Code   RO         5679    i.VL53L0X_SetSequenceStepEnable  vl53l0x_api.o
    0x08008b16   0x08008b16   0x00000002   PAD
    0x08008b18   0x08008b18   0x000000d4   Code   RO         5688    i.VL53L0X_StartMeasurement  vl53l0x_api.o
    0x08008bec   0x08008bec   0x00000174   Code   RO         5689    i.VL53L0X_StaticInit  vl53l0x_api.o
    0x08008d60   0x08008d60   0x00000030   Code   RO         5480    i.VL53L0X_UpdateByte  vl53l0x_i2c_dev.o
    0x08008d90   0x08008d90   0x0000000e   Code   RO         5481    i.VL53L0X_WrByte    vl53l0x_i2c_dev.o
    0x08008d9e   0x08008d9e   0x00000028   Code   RO         5482    i.VL53L0X_WrDWord   vl53l0x_i2c_dev.o
    0x08008dc6   0x08008dc6   0x0000001c   Code   RO         5483    i.VL53L0X_WrWord    vl53l0x_i2c_dev.o
    0x08008de2   0x08008de2   0x00000014   Code   RO         5484    i.VL53L0X_WriteMulti  vl53l0x_i2c_dev.o
    0x08008df6   0x08008df6   0x00000002   PAD
    0x08008df8   0x08008df8   0x000000e0   Code   RO         6345    i.VL53L0X_calc_dmax  vl53l0x_api_core.o
    0x08008ed8   0x08008ed8   0x00000010   Code   RO         6346    i.VL53L0X_calc_macro_period_ps  vl53l0x_api_core.o
    0x08008ee8   0x08008ee8   0x00000238   Code   RO         6347    i.VL53L0X_calc_sigma_estimate  vl53l0x_api_core.o
    0x08009120   0x08009120   0x00000022   Code   RO         6348    i.VL53L0X_calc_timeout_mclks  vl53l0x_api_core.o
    0x08009142   0x08009142   0x00000022   Code   RO         6349    i.VL53L0X_calc_timeout_us  vl53l0x_api_core.o
    0x08009164   0x08009164   0x0000000c   Code   RO         6350    i.VL53L0X_decode_timeout  vl53l0x_api_core.o
    0x08009170   0x08009170   0x00000008   Code   RO         6351    i.VL53L0X_decode_vcsel_period  vl53l0x_api_core.o
    0x08009178   0x08009178   0x0000004a   Code   RO         6352    i.VL53L0X_device_read_strobe  vl53l0x_api_core.o
    0x080091c2   0x080091c2   0x00000020   Code   RO         6353    i.VL53L0X_encode_timeout  vl53l0x_api_core.o
    0x080091e2   0x080091e2   0x0000052a   Code   RO         6355    i.VL53L0X_get_info_from_device  vl53l0x_api_core.o
    0x0800970c   0x0800970c   0x000000c6   Code   RO         6356    i.VL53L0X_get_measurement_timing_budget_micro_seconds  vl53l0x_api_core.o
    0x080097d2   0x080097d2   0x00000034   Code   RO         6202    i.VL53L0X_get_offset_calibration_data_micro_meter  vl53l0x_api_calibration.o
    0x08009806   0x08009806   0x00000002   PAD
    0x08009808   0x08009808   0x000001e2   Code   RO         6357    i.VL53L0X_get_pal_range_status  vl53l0x_api_core.o
    0x080099ea   0x080099ea   0x0000001c   Code   RO         6358    i.VL53L0X_get_total_signal_rate  vl53l0x_api_core.o
    0x08009a06   0x08009a06   0x0000002e   Code   RO         6359    i.VL53L0X_get_total_xtalk_rate  vl53l0x_api_core.o
    0x08009a34   0x08009a34   0x00000030   Code   RO         6360    i.VL53L0X_get_vcsel_pulse_period  vl53l0x_api_core.o
    0x08009a64   0x08009a64   0x0000002a   Code   RO         6361    i.VL53L0X_isqrt     vl53l0x_api_core.o
    0x08009a8e   0x08009a8e   0x000000a4   Code   RO         6362    i.VL53L0X_load_tuning_settings  vl53l0x_api_core.o
    0x08009b32   0x08009b32   0x00000036   Code   RO         6363    i.VL53L0X_measurement_poll_for_completion  vl53l0x_api_core.o
    0x08009b68   0x08009b68   0x00000074   Code   RO         6206    i.VL53L0X_perform_phase_calibration  vl53l0x_api_calibration.o
    0x08009bdc   0x08009bdc   0x00000040   Code   RO         6207    i.VL53L0X_perform_ref_calibration  vl53l0x_api_calibration.o
    0x08009c1c   0x08009c1c   0x0000021c   Code   RO         6208    i.VL53L0X_perform_ref_spad_management  vl53l0x_api_calibration.o
    0x08009e38   0x08009e38   0x00000038   Code   RO         6209    i.VL53L0X_perform_single_ref_calibration  vl53l0x_api_calibration.o
    0x08009e70   0x08009e70   0x00000074   Code   RO         6210    i.VL53L0X_perform_vhv_calibration  vl53l0x_api_calibration.o
    0x08009ee4   0x08009ee4   0x000000b2   Code   RO         6212    i.VL53L0X_ref_calibration_io  vl53l0x_api_calibration.o
    0x08009f96   0x08009f96   0x000000c4   Code   RO         6366    i.VL53L0X_set_measurement_timing_budget_micro_seconds  vl53l0x_api_core.o
    0x0800a05a   0x0800a05a   0x000000a6   Code   RO         6215    i.VL53L0X_set_reference_spads  vl53l0x_api_calibration.o
    0x0800a100   0x0800a100   0x00000020   Code   RO         6820    i.__0printf         mc_w.l(printfa.o)
    0x0800a120   0x0800a120   0x00000024   Code   RO         6826    i.__0vsprintf       mc_w.l(printfa.o)
    0x0800a144   0x0800a144   0x00000022   Code   RO          599    i.__NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x0800a166   0x0800a166   0x00000002   PAD
    0x0800a168   0x0800a168   0x00000008   Code   RO         6879    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x0800a170   0x0800a170   0x00000038   Code   RO         6552    i.__hardfp_atof     m_wv.l(atof.o)
    0x0800a1a8   0x0800a1a8   0x0000000c   Code   RO         6880    i.__read_errno      mc_w.l(errno.o)
    0x0800a1b4   0x0800a1b4   0x0000000e   Code   RO         6962    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800a1c2   0x0800a1c2   0x00000002   Code   RO         6963    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800a1c4   0x0800a1c4   0x0000000e   Code   RO         6964    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800a1d2   0x0800a1d2   0x00000002   PAD
    0x0800a1d4   0x0800a1d4   0x0000000c   Code   RO         6881    i.__set_errno       mc_w.l(errno.o)
    0x0800a1e0   0x0800a1e0   0x00000184   Code   RO         6827    i._fp_digits        mc_w.l(printfa.o)
    0x0800a364   0x0800a364   0x0000000e   Code   RO         6945    i._is_digit         mc_w.l(scanf_fp.o)
    0x0800a372   0x0800a372   0x00000002   PAD
    0x0800a374   0x0800a374   0x000006b4   Code   RO         6828    i._printf_core      mc_w.l(printfa.o)
    0x0800aa28   0x0800aa28   0x00000024   Code   RO         6829    i._printf_post_padding  mc_w.l(printfa.o)
    0x0800aa4c   0x0800aa4c   0x0000002e   Code   RO         6830    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800aa7a   0x0800aa7a   0x0000000a   Code   RO         6832    i._sputc            mc_w.l(printfa.o)
    0x0800aa84   0x0800aa84   0x0000009c   Code   RO         4593    i.arraySearch       core_json.o
    0x0800ab20   0x0800ab20   0x00000068   Code   RO         5205    i.atgm336h_init     atgm336h.o
    0x0800ab88   0x0800ab88   0x000002ac   Code   RO         5207    i.atgm336h_usart3_irq_handler  atgm336h.o
    0x0800ae34   0x0800ae34   0x00000034   Code   RO         5411    i.clearV831Data     v831.o
    0x0800ae68   0x0800ae68   0x00000070   Code   RO         5208    i.clrStruct         atgm336h.o
    0x0800aed8   0x0800aed8   0x0000000c   Code   RO         5375    i.delay_init        delay.o
    0x0800aee4   0x0800aee4   0x00000004   Code   RO         5376    i.delay_ms          delay.o
    0x0800aee8   0x0800aee8   0x00000024   Code   RO         5377    i.delay_us          delay.o
    0x0800af0c   0x0800af0c   0x00000058   Code   RO         4733    i.dht11_check       dht11.o
    0x0800af64   0x0800af64   0x0000001a   Code   RO         4734    i.dht11_init        dht11.o
    0x0800af7e   0x0800af7e   0x00000002   PAD
    0x0800af80   0x0800af80   0x00000028   Code   RO         4735    i.dht11_io_in       dht11.o
    0x0800afa8   0x0800afa8   0x00000028   Code   RO         4736    i.dht11_io_out      dht11.o
    0x0800afd0   0x0800afd0   0x00000058   Code   RO         4737    i.dht11_read_bit    dht11.o
    0x0800b028   0x0800b028   0x0000001a   Code   RO         4738    i.dht11_read_byte   dht11.o
    0x0800b042   0x0800b042   0x00000050   Code   RO         4739    i.dht11_read_data   dht11.o
    0x0800b092   0x0800b092   0x00000002   PAD
    0x0800b094   0x0800b094   0x00000030   Code   RO         4740    i.dht11_rst         dht11.o
    0x0800b0c4   0x0800b0c4   0x00000094   Code   RO         6217    i.enable_ref_spads  vl53l0x_api_calibration.o
    0x0800b158   0x0800b158   0x0000002a   Code   RO         6218    i.enable_spad_bit   vl53l0x_api_calibration.o
    0x0800b182   0x0800b182   0x00000002   PAD
    0x0800b184   0x0800b184   0x0000009c   Code   RO         4805    i.esp8266_receive_msg  esp8266.o
    0x0800b220   0x0800b220   0x00000018   Code   RO         5123    i.fputc             uart_printf.o
    0x0800b238   0x0800b238   0x00000038   Code   RO         4594    i.getType           core_json.o
    0x0800b270   0x0800b270   0x00000060   Code   RO         6219    i.get_next_good_spad  vl53l0x_api_calibration.o
    0x0800b2d0   0x0800b2d0   0x0000000a   Code   RO         6220    i.get_ref_spad_map  vl53l0x_api_calibration.o
    0x0800b2da   0x0800b2da   0x000000f4   Code   RO         6368    i.get_sequence_step_timeout  vl53l0x_api_core.o
    0x0800b3ce   0x0800b3ce   0x00000022   Code   RO         4595    i.hexToInt          core_json.o
    0x0800b3f0   0x0800b3f0   0x00000018   Code   RO         6221    i.is_aperture       vl53l0x_api_calibration.o
    0x0800b408   0x0800b408   0x0000008e   Code   RO           16    i.main              main.o
    0x0800b496   0x0800b496   0x000000fc   Code   RO         4597    i.multiSearch       core_json.o
    0x0800b592   0x0800b592   0x00000098   Code   RO         4598    i.nextKeyValuePair  core_json.o
    0x0800b62a   0x0800b62a   0x00000064   Code   RO         4599    i.nextValue         core_json.o
    0x0800b68e   0x0800b68e   0x000000b2   Code   RO         4600    i.objectSearch      core_json.o
    0x0800b740   0x0800b740   0x0000002c   Code   RO         4991    i.oled_write_byte   oled.o
    0x0800b76c   0x0800b76c   0x000001b0   Code   RO         5209    i.parseGpsBuffer    atgm336h.o
    0x0800b91c   0x0800b91c   0x00000360   Code   RO         5412    i.parseV831Buffer   v831.o
    0x0800bc7c   0x0800bc7c   0x00000090   Code   RO         4809    i.parse_json_msg    esp8266.o
    0x0800bd0c   0x0800bd0c   0x00000064   Code   RO         6222    i.perform_ref_signal_measurement  vl53l0x_api_calibration.o
    0x0800bd70   0x0800bd70   0x000002bc   Code   RO         5210    i.printGpsBuffer    atgm336h.o
    0x0800c02c   0x0800c02c   0x00000068   Code   RO         5413    i.printV831Data     v831.o
    0x0800c094   0x0800c094   0x00000038   Code   RO         5693    i.sequence_step_enabled  vl53l0x_api.o
    0x0800c0cc   0x0800c0cc   0x00000018   Code   RO         4898    i.set_led           led.o
    0x0800c0e4   0x0800c0e4   0x0000000a   Code   RO         6223    i.set_ref_spad_map  vl53l0x_api_calibration.o
    0x0800c0ee   0x0800c0ee   0x00000104   Code   RO         6369    i.set_sequence_step_timeout  vl53l0x_api_core.o
    0x0800c1f2   0x0800c1f2   0x00000002   PAD
    0x0800c1f4   0x0800c1f4   0x00000074   Code   RO         4601    i.skipAnyScalar     core_json.o
    0x0800c268   0x0800c268   0x000000a8   Code   RO         4602    i.skipCollection    core_json.o
    0x0800c310   0x0800c310   0x00000078   Code   RO         4603    i.skipDigits        core_json.o
    0x0800c388   0x0800c388   0x000000b4   Code   RO         4604    i.skipEscape        core_json.o
    0x0800c43c   0x0800c43c   0x0000003a   Code   RO         4605    i.skipLiteral       core_json.o
    0x0800c476   0x0800c476   0x000000b6   Code   RO         4606    i.skipNumber        core_json.o
    0x0800c52c   0x0800c52c   0x00000088   Code   RO         4607    i.skipObjectScalars  core_json.o
    0x0800c5b4   0x0800c5b4   0x00000070   Code   RO         4608    i.skipOneHexEscape  core_json.o
    0x0800c624   0x0800c624   0x0000005c   Code   RO         4609    i.skipScalars       core_json.o
    0x0800c680   0x0800c680   0x0000002c   Code   RO         4610    i.skipSpace         core_json.o
    0x0800c6ac   0x0800c6ac   0x00000050   Code   RO         4611    i.skipSpaceAndComma  core_json.o
    0x0800c6fc   0x0800c6fc   0x00000062   Code   RO         4612    i.skipString        core_json.o
    0x0800c75e   0x0800c75e   0x00000028   Code   RO         4613    i.strnEq            core_json.o
    0x0800c786   0x0800c786   0x00000002   PAD
    0x0800c788   0x0800c788   0x0000000c   Code   RO         5091    i.time_slot_start   time_handle.o
    0x0800c794   0x0800c794   0x00000020   Code   RO         4810    i.uart2_receiver_clear  esp8266.o
    0x0800c7b4   0x0800c7b4   0x00000048   Code   RO         4811    i.uart2_receiver_handle  esp8266.o
    0x0800c7fc   0x0800c7fc   0x000000bc   Code   RO         4925    i.user_init_program  main_program.o
    0x0800c8b8   0x0800c8b8   0x000001a4   Code   RO         4926    i.user_main_program  main_program.o
    0x0800ca5c   0x0800ca5c   0x00000040   Code   RO         5414    i.v831_init         v831.o
    0x0800ca9c   0x0800ca9c   0x0000024c   Code   RO         5416    i.v831_usart6_irq_handler  v831.o
    0x0800cce8   0x0800cce8   0x00000008   Data   RO         1368    .constdata          stm32h7xx_hal_dma.o
    0x0800ccf0   0x0800ccf0   0x00000018   Data   RO         4080    .constdata          stm32h7xx_hal_uart.o
    0x0800cd08   0x0800cd08   0x00000010   Data   RO         4446    .constdata          stm32h7xx_hal_uart_ex.o
    0x0800cd18   0x0800cd18   0x00000010   Data   RO         4552    .constdata          system_stm32h7xx.o
    0x0800cd28   0x0800cd28   0x00000874   Data   RO         4993    .constdata          oled.o
    0x0800d59c   0x0800d59c   0x00000081   Data   RO         6914    .constdata          mc_w.l(ctype_o.o)
    0x0800d61d   0x0800d61d   0x00000003   PAD
    0x0800d620   0x0800d620   0x00000004   Data   RO         6915    .constdata          mc_w.l(ctype_o.o)
    0x0800d624   0x0800d624   0x0000004e   Data   RO         4813    .conststring        esp8266.o
    0x0800d672   0x0800d672   0x00000002   PAD
    0x0800d674   0x0800d674   0x000000d2   Data   RO         5418    .conststring        v831.o
    0x0800d746   0x0800d746   0x00000002   PAD
    0x0800d748   0x0800d748   0x00000020   Data   RO         6960    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800d768, Size: 0x00000000, Max: 0x00020000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM2 (Exec base: 0x24000000, Load base: 0x0800d768, Size: 0x00001c90, Max: 0x00050000, ABSOLUTE, COMPRESSED[0x000002a8])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x24000000   COMPRESSED   0x0000000c   Data   RW         2100    .data               stm32h7xx_hal.o
    0x2400000c   COMPRESSED   0x00000008   Data   RW         4553    .data               system_stm32h7xx.o
    0x24000014   COMPRESSED   0x00000006   Data   RW         4814    .data               esp8266.o
    0x2400001a   COMPRESSED   0x00000002   PAD
    0x2400001c   COMPRESSED   0x0000000c   Data   RW         4927    .data               main_program.o
    0x24000028   COMPRESSED   0x00000008   Data   RW         4994    .data               oled.o
    0x24000030   COMPRESSED   0x00000010   Data   RW         5092    .data               time_handle.o
    0x24000040   COMPRESSED   0x00000008   Data   RW         5212    .data               atgm336h.o
    0x24000048   COMPRESSED   0x0000000c   Data   RW         5276    .data               motor.o
    0x24000054   COMPRESSED   0x00000004   Data   RW         5335    .data               servo.o
    0x24000058   COMPRESSED   0x00000004   Data   RW         5419    .data               v831.o
    0x2400005c   COMPRESSED   0x00000008   Data   RW         5487    .data               vl53l0x_i2c_dev.o
    0x24000064   COMPRESSED   0x000002a9   Data   RW         5694    .data               vl53l0x_api.o
    0x2400030d   COMPRESSED   0x00000003   PAD
    0x24000310   COMPRESSED   0x00000010   Data   RW         6224    .data               vl53l0x_api_calibration.o
    0x24000320   COMPRESSED   0x00000004   Data   RW         6580    .data               mc_w.l(strtok.o)
    0x24000324   COMPRESSED   0x00000004   Data   RW         6872    .data               mc_w.l(stdout.o)
    0x24000328   COMPRESSED   0x00000004   Data   RW         6882    .data               mc_w.l(errno.o)
    0x2400032c        -       0x000000a8   Zero   RW          257    .bss                i2c.o
    0x240003d4        -       0x00000130   Zero   RW          310    .bss                tim.o
    0x24000504        -       0x00000340   Zero   RW          385    .bss                usart.o
    0x24000844        -       0x00000200   Zero   RW         4812    .bss                esp8266.o
    0x24000a44        -       0x00000480   Zero   RW         4992    .bss                oled.o
    0x24000ec4        -       0x000001c0   Zero   RW         5211    .bss                atgm336h.o
    0x24001084        -       0x00000690   Zero   RW         5417    .bss                v831.o
    0x24001714        -       0x00000160   Zero   RW         5485    .bss                vl53l0x_i2c_dev.o
    0x24001874        -       0x0000001c   Zero   RW         5486    .bss                vl53l0x_i2c_dev.o
    0x24001890        -       0x00000400   Zero   RW            1    STACK               startup_stm32h723xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2032        718          0          8        448       6769   atgm336h.o
      2566         30          0          0          0      24274   core_json.o
        52         10          0          0          0       1535   delay.o
       436         24          0          0          0       4911   dht11.o
        60          4          0          0          0        806   dma.o
       404        104         78          6        512       6983   esp8266.o
       200         10          0          0          0       1071   gpio.o
       420         46          0          0        168       2566   i2c.o
        24          6          0          0          0        486   led.o
       296          4          0          0          0    1201382   main.o
       608        212          0         12          0       2112   main_program.o
       182         24          0         12          0       3437   motor.o
       744         36       2164          8       1152       7516   oled.o
        68         16          0          4          0       1907   servo.o
        84          8          0          0          0       2542   st_i2c.o
        44         12        716          0       1024        852   startup_stm32h723xx.o
       220         40          0         12          0      23717   stm32h7xx_hal.o
       354         28          0          0          0      41865   stm32h7xx_hal_cortex.o
      5984        242          8          0          0      12653   stm32h7xx_hal_dma.o
       560         44          0          0          0       2911   stm32h7xx_hal_gpio.o
      2082         48          0          0          0      13996   stm32h7xx_hal_i2c.o
       172          0          0          0          0       2003   stm32h7xx_hal_i2c_ex.o
        28          6          0          0          0        838   stm32h7xx_hal_msp.o
        84          6          0          0          0        801   stm32h7xx_hal_pwr_ex.o
      2374        102          0          0          0       7100   stm32h7xx_hal_rcc.o
      3622        112          0          0          0       7785   stm32h7xx_hal_rcc_ex.o
      3186        346          0          0          0      21870   stm32h7xx_hal_tim.o
       190         38          0          0          0       3204   stm32h7xx_hal_tim_ex.o
      3724        166         24          0          0      38552   stm32h7xx_hal_uart.o
       290          6         16          0          0       5498   stm32h7xx_hal_uart_ex.o
       124         34          0          0          0       7308   stm32h7xx_it.o
       244         38         16          8          0       1921   system_stm32h7xx.o
       760         70          0          0        304       5337   tim.o
       120         14          0         16          0       1985   time_handle.o
        24          4          0          0          0        516   uart_printf.o
      1032         88          0          0        832       4463   usart.o
      1672        568        210          4       1680       5503   v831.o
      3244         62          0        681          0      31841   vl53l0x_api.o
      1718          6          0         16          0      17029   vl53l0x_api_calibration.o
      4086         16          0          0          0      26336   vl53l0x_api_core.o
       360         10          0          8        380      24175   vl53l0x_i2c_dev.o

    ----------------------------------------------------------------------
     44532       <USER>       <GROUP>        800       6500    1578356   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        58          0          4          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56         12          0          0          0        132   atof.o
        56          8          0          0          0         84   __0sscanf.o
        94          0          0          0          0          0   __dclz77c.o
        28          0          0          0          0         68   _chval.o
       816          6          0          0          0        112   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        32         16          0          4          0        204   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        18          0          0          0          0         76   isspace_o.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2264         96          0          0          0        612   printfa.o
        40          8          0          0          0         84   scanf_char.o
       878         12          0          0          0        216   scanf_fp.o
         0          0          0          4          0          0   stdout.o
        20          0          0          0          0         68   strchr.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       156         12          0          0          0        120   strtod.o
        68          6          0          4          0         80   strtok.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      7208        <USER>        <GROUP>         12          0       4360   Library Totals
         8          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        56         12          0          0          0        132   m_wv.l
      5888        184        133         12          0       3240   mc_w.l
      1256          0          0          0          0        988   mf_w.l

    ----------------------------------------------------------------------
      7208        <USER>        <GROUP>         12          0       4360   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     51740       3554       3404        812       6500    1551012   Grand Totals
     51740       3554       3404        680       6500    1551012   ELF Image Totals (compressed)
     51740       3554       3404        680          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                55144 (  53.85kB)
    Total RW  Size (RW Data + ZI Data)              7312 (   7.14kB)
    Total ROM Size (Code + RO Data + RW Data)      55824 (  54.52kB)

==============================================================================

