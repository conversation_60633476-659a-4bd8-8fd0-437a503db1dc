<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [Defect\Defect.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image Defect\Defect.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Sep 18 16:47:25 2025
<BR><P>
<H3>Maximum Stack Usage =        832 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; user_main_program &rArr; esp8266_receive_msg &rArr; parse_json_msg &rArr; JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[74]">ADC3_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[74]">ADC3_IRQHandler</a><BR>
 <LI><a href="#[5]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">BusFault_Handler</a><BR>
 <LI><a href="#[3]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">HardFault_Handler</a><BR>
 <LI><a href="#[4]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">MemManage_Handler</a><BR>
 <LI><a href="#[2]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">NMI_Handler</a><BR>
 <LI><a href="#[119]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[119]">UART_EndTxTransfer</a><BR>
 <LI><a href="#[6]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[74]">ADC3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1d]">ADC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[76]">BDMA_Channel0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[77]">BDMA_Channel1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[78]">BDMA_Channel2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[79]">BDMA_Channel3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7a]">BDMA_Channel4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7b]">BDMA_Channel5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7c]">BDMA_Channel6_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7d]">BDMA_Channel7_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from stm32h7xx_it.o(i.BusFault_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[62]">CEC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7e]">COMP1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8b]">CORDIC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[84]">CRS_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[54]">DCMI_PSSI_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[67]">DFSDM1_FLT0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[68]">DFSDM1_FLT1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[69]">DFSDM1_FLT2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6a]">DFSDM1_FLT3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream0_IRQHandler</a> from stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream1_IRQHandler</a> from stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream6_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream7_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5f]">DMA2D_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[42]">DMA2_Stream0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream6_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream7_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[66]">DMAMUX1_OVR_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[75]">DMAMUX2_OVR_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[87]">DTS_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from stm32h7xx_it.o(i.DebugMon_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[85]">ECC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[47]">ETH_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[48]">ETH_WKUP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[33]">EXTI15_10_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[22]">EXTI9_5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[95]">ExitRun0Mode</a> from system_stm32h7xx.o(i.ExitRun0Mode) referenced from startup_stm32h723xx.o(.text)
 <LI><a href="#[1e]">FDCAN1_IT0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[20]">FDCAN1_IT1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1f]">FDCAN2_IT0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[21]">FDCAN2_IT1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[90]">FDCAN3_IT0_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[91]">FDCAN3_IT1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[49]">FDCAN_CAL_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[f]">FLASH_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8a]">FMAC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3a]">FMC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[56]">FPU_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[73]">HSEM1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from stm32h7xx_it.o(i.HardFault_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2b]">I2C1_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2a]">I2C1_EV_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2d]">I2C2_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2c]">I2C2_EV_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4f]">I2C3_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4e]">I2C3_EV_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[64]">I2C4_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[63]">I2C4_EV_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8f]">I2C5_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8e]">I2C5_EV_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[61]">LPTIM1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7f]">LPTIM2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[80]">LPTIM3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[81]">LPTIM4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[82]">LPTIM5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[83]">LPUART1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5e]">LTDC_ER_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5d]">LTDC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[70]">MDIOS_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6f]">MDIOS_WKUP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[71]">MDMA_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from stm32h7xx_it.o(i.MemManage_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from stm32h7xx_it.o(i.NMI_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[60]">OCTOSPI1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[89]">OCTOSPI2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[51]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[50]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[53]">OTG_HS_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[52]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[c]">PVD_AVD_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from stm32h7xx_it.o(i.PendSV_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[10]">RCC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[55]">RNG_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5c]">SAI1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[86]">SAI4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3b]">SDMMC1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[72]">SDMMC2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[65]">SPDIF_RX_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2e]">SPI1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[2f]">SPI2_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[59]">SPI4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5a]">SPI5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[5b]">SPI6_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from stm32h7xx_it.o(i.SVC_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6b]">SWPMI1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from stm32h7xx_it.o(i.SysTick_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[96]">SystemInit</a> from system_stm32h7xx.o(i.SystemInit) referenced from startup_stm32h723xx.o(.text)
 <LI><a href="#[d]">TAMP_STAMP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6c]">TIM15_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6d]">TIM16_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6e]">TIM17_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[23]">TIM1_BRK_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[26]">TIM1_CC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[25]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[24]">TIM1_UP_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[92]">TIM23_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[93]">TIM24_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[27]">TIM2_IRQHandler</a> from stm32h7xx_it.o(i.TIM2_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[28]">TIM3_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[29]">TIM4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[40]">TIM6_DAC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[36]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[57]">UART7_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[58]">UART8_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[8c]">UART9_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[9c]">UART_DMAAbortOnError</a> from stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[9f]">UART_DMAError</a> from stm32h7xx_hal_uart.o(i.UART_DMAError) referenced from stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[9d]">UART_DMAReceiveCplt</a> from stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[9e]">UART_DMARxHalfCplt</a> from stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[8d]">USART10_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[30]">USART1_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[31]">USART2_IRQHandler</a> from stm32h7xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[32]">USART3_IRQHandler</a> from stm32h7xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[4d]">USART6_IRQHandler</a> from stm32h7xx_it.o(i.USART6_IRQHandler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from stm32h7xx_it.o(i.UsageFault_Handler) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[88]">WAKEUP_PIN_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_stm32h723xx.o(.text) referenced from startup_stm32h723xx.o(RESET)
 <LI><a href="#[97]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32h723xx.o(.text)
 <LI><a href="#[99]">_sbackspace</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[9a]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[98]">_sgetc</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[a1]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0vsprintf)
 <LI><a href="#[a0]">fputc</a> from uart_printf.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[9b]">isspace</a> from isspace_o.o(.text) referenced 2 times from scanf_char.o(.text)
 <LI><a href="#[94]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[97]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(.text)
</UL>
<P><STRONG><a name="[1f9]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[a2]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[c0]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1fa]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1fb]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1fc]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1fd]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[1fe]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1ff]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>BDMA_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>BDMA_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>BDMA_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>BDMA_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>BDMA_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>BDMA_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>BDMA_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>BDMA_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>COMP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>CORDIC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DCMI_PSSI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DFSDM1_FLT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>DFSDM1_FLT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DFSDM1_FLT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>DFSDM1_FLT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>DMAMUX1_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DMAMUX2_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>DTS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>ECC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>FDCAN1_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>FDCAN1_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FDCAN2_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>FDCAN2_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>FDCAN3_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>FDCAN3_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>FDCAN_CAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>FMAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>HSEM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>I2C4_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>I2C4_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>I2C5_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>I2C5_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>LPTIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>LPTIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>LPTIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>MDIOS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>MDIOS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>MDMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>OCTOSPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>OCTOSPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_AVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>SAI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDMMC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>SDMMC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SPDIF_RX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>SWPMI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>TIM23_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>TIM24_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>UART9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>USART10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>WAKEUP_PIN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h723xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[a4]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1c1]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
</UL>

<P><STRONG><a name="[167]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetRangingMeasurementData
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
</UL>

<P><STRONG><a name="[200]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[201]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[202]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[a7]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clrStruct
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_receiver_clear
</UL>

<P><STRONG><a name="[df]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clearV831Data
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clrStruct
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_out
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_in
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[203]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[a9]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1d1]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printGpsBuffer
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[1e1]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseGpsBuffer
</UL>

<P><STRONG><a name="[1e4]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printGpsBuffer
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
</UL>

<P><STRONG><a name="[1d2]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printGpsBuffer
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseGpsBuffer
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[1a1]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
</UL>

<P><STRONG><a name="[1e2]"></a>strtok</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseGpsBuffer
</UL>

<P><STRONG><a name="[aa]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[ac]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[c1]"></a>_scanf_string</STRONG> (Thumb, 224 bytes, Stack size 56 bytes, _scanf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_string
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[ae]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_json_msg
</UL>

<P><STRONG><a name="[204]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1ba]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[205]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[a5]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[206]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[ad]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[ab]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[98]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> strtod.o(.text)
<LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[99]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> strtod.o(.text)
<LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[b4]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[b0]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[207]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[b7]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[bc]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[bd]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[be]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[bf]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1b7]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a3]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[208]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[209]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[b5]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[9b]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 2]<UL><LI> scanf_char.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[b1]"></a>__vfscanf</STRONG> (Thumb, 810 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_string
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[b3]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[c4]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[b6]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[ba]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[b9]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>

<P><STRONG><a name="[c3]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[c6]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[20a]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[20b]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[20c]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[20d]"></a>__decompress2</STRONG> (Thumb, 94 bytes, Stack size unknown bytes, __dclz77c.o(.text), UNUSED)

<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DMA1_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.DMA1_Stream1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DMA1_Stream1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1f8]"></a>Encoder_GetDistanceMM</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, motor.o(i.Encoder_GetDistanceMM))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[c8]"></a>Encoder_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, motor.o(i.Encoder_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Encoder_Init &rArr; HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1f7]"></a>Encoder_Update</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, motor.o(i.Encoder_Update))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[e1]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
</UL>

<P><STRONG><a name="[95]"></a>ExitRun0Mode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, system_stm32h7xx.o(i.ExitRun0Mode))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(.text)
</UL>
<P><STRONG><a name="[ca]"></a>HAL_DMA_Abort</STRONG> (Thumb, 836 bytes, Stack size 40 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[11c]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 602 bytes, Stack size 40 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[118]"></a>HAL_DMA_GetError</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_GetError))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[c7]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 1756 bytes, Stack size 48 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream1_IRQHandler
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[cc]"></a>HAL_DMA_Init</STRONG> (Thumb, 922 bytes, Stack size 40 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXRequestGenBaseAndMask
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXChannelBaseAndMask
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[d1]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 628 bytes, Stack size 40 bytes, stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[d3]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32h7xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_init
</UL>

<P><STRONG><a name="[e2]"></a>HAL_GPIO_Init</STRONG> (Thumb, 496 bytes, Stack size 40 bytes, stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_out
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_in
</UL>

<P><STRONG><a name="[1c8]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_check
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_bit
</UL>

<P><STRONG><a name="[139]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Control
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_led
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
</UL>

<P><STRONG><a name="[cb]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL3_Config
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL2_Config
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsErrorOccurred
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clearV831Data
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clrStruct
</UL>

<P><STRONG><a name="[13b]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 88 bytes, Stack size 12 bytes, stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_I2CEx_ConfigAnalogFilter
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[13c]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 84 bytes, Stack size 12 bytes, stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_I2CEx_ConfigDigitalFilter
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[d4]"></a>HAL_I2C_Init</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32h7xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[d6]"></a>HAL_I2C_Master_Transmit</STRONG> (Thumb, 324 bytes, Stack size 56 bytes, stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_TransferConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_byte
</UL>

<P><STRONG><a name="[db]"></a>HAL_I2C_Mem_Read</STRONG> (Thumb, 346 bytes, Stack size 64 bytes, stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_TransferConfig
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>

<P><STRONG><a name="[dd]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 340 bytes, Stack size 64 bytes, stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_TransferConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteBytes
</UL>

<P><STRONG><a name="[d5]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 222 bytes, Stack size 232 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[156]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[e3]"></a>HAL_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32h7xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e6]"></a>HAL_InitTick</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32h7xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[1d7]"></a>HAL_MPU_ConfigRegion</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_MPU_ConfigRegion
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d6]"></a>HAL_MPU_Disable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d8]"></a>HAL_MPU_Enable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>HAL_MspInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32h7xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f7]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[e9]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[e4]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[eb]"></a>HAL_PWREx_ConfigSupply</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PWREx_ConfigSupply
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[ec]"></a>HAL_RCCEx_GetD3PCLK1Freq</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_RCCEx_GetD3PCLK1Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[15a]"></a>HAL_RCCEx_GetPLL2ClockFreq</STRONG> (Thumb, 296 bytes, Stack size 12 bytes, stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_RCCEx_GetPLL2ClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[15b]"></a>HAL_RCCEx_GetPLL3ClockFreq</STRONG> (Thumb, 296 bytes, Stack size 12 bytes, stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_RCCEx_GetPLL3ClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[e0]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 2362 bytes, Stack size 48 bytes, stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL3_Config
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL2_Config
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>

<P><STRONG><a name="[f0]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 580 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[ed]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetD3PCLK1Freq
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[f1]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_GetPCLK1Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[f2]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_GetPCLK2Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[e5]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 278 bytes, Stack size 16 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>

<P><STRONG><a name="[f3]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1318 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[e8]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[106]"></a>HAL_TIMEx_Break2Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[105]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[108]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 146 bytes, Stack size 20 bytes, stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[f4]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[f5]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[1c4]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[1f1]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 118 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time_slot_start
</UL>

<P><STRONG><a name="[f8]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 248 bytes, Stack size 16 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_TI2_ConfigInputStage
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[fd]"></a>HAL_TIM_Encoder_Init</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[fe]"></a>HAL_TIM_Encoder_MspInit</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, tim.o(i.HAL_TIM_Encoder_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
</UL>

<P><STRONG><a name="[c9]"></a>HAL_TIM_Encoder_Start</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Init
</UL>

<P><STRONG><a name="[101]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[100]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 328 bytes, Stack size 24 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_Break2Callback
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[109]"></a>HAL_TIM_MspPostInit</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, tim.o(i.HAL_TIM_MspPostInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
</UL>

<P><STRONG><a name="[102]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[10a]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 292 bytes, Stack size 16 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_TIM_PWM_ConfigChannel &rArr; TIM_OC1_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC6_SetConfig
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC5_SetConfig
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4_SetConfig
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3_SetConfig
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
</UL>

<P><STRONG><a name="[111]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
</UL>

<P><STRONG><a name="[112]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[103]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[113]"></a>HAL_TIM_PWM_Start</STRONG> (Thumb, 244 bytes, Stack size 12 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SERVO_Init
</UL>

<P><STRONG><a name="[104]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 100 bytes, Stack size 12 bytes, time_handle.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[107]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[144]"></a>HAL_UARTEx_DisableFifoMode</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_UARTEx_DisableFifoMode
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[11e]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[122]"></a>HAL_UARTEx_RxFifoFullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[114]"></a>HAL_UARTEx_SetRxFifoThreshold</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_UARTEx_SetRxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[116]"></a>HAL_UARTEx_SetTxFifoThreshold</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_UARTEx_SetTxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[121]"></a>HAL_UARTEx_TxFifoEmptyCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>HAL_UARTEx_WakeupCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[117]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
</UL>

<P><STRONG><a name="[11d]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[11b]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 898 bytes, Stack size 24 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_IRQHandler &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_WakeupCallback
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_TxFifoEmptyCallback
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxFifoFullCallback
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[123]"></a>HAL_UART_Init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[124]"></a>HAL_UART_MspInit</STRONG> (Thumb, 568 bytes, Stack size 248 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[128]"></a>HAL_UART_Receive</STRONG> (Thumb, 232 bytes, Stack size 40 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_Receive &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_receiver_handle
</UL>

<P><STRONG><a name="[12a]"></a>HAL_UART_Receive_DMA</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
</UL>

<P><STRONG><a name="[158]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[159]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[12c]"></a>HAL_UART_Transmit</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[120]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[12f]"></a>JSON_SearchConst</STRONG> (Thumb, 100 bytes, Stack size 40 bytes, core_json.o(i.JSON_SearchConst))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiSearch
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getType
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_SearchT
</UL>

<P><STRONG><a name="[132]"></a>JSON_SearchT</STRONG> (Thumb, 18 bytes, Stack size 32 bytes, core_json.o(i.JSON_SearchT))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_SearchConst
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_json_msg
</UL>

<P><STRONG><a name="[133]"></a>JSON_Validate</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, core_json.o(i.JSON_Validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = JSON_Validate &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipCollection
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_json_msg
</UL>

<P><STRONG><a name="[137]"></a>MX_DMA_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[138]"></a>MX_GPIO_Init</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13a]"></a>MX_I2C1_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13d]"></a>MX_I2C2_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13e]"></a>MX_TIM1_Init</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, tim.o(i.MX_TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_TIM1_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[140]"></a>MX_TIM2_Init</STRONG> (Thumb, 96 bytes, Stack size 40 bytes, tim.o(i.MX_TIM2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_TIM2_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[141]"></a>MX_TIM3_Init</STRONG> (Thumb, 96 bytes, Stack size 56 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[142]"></a>MX_TIM4_Init</STRONG> (Thumb, 148 bytes, Stack size 72 bytes, tim.o(i.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[143]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[145]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[146]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[147]"></a>MX_USART6_UART_Init</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, usart.o(i.MX_USART6_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = MX_USART6_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[148]"></a>Motor_Control</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, motor.o(i.Motor_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Motor_Control
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1f3]"></a>Motor_Init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, motor.o(i.Motor_Init))
<BR><BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[151]"></a>OLED_draw_point</STRONG> (Thumb, 68 bytes, Stack size 12 bytes, oled.o(i.OLED_draw_point))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = OLED_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_show_char
</UL>

<P><STRONG><a name="[149]"></a>OLED_init</STRONG> (Thumb, 230 bytes, Stack size 8 bytes, oled.o(i.OLED_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_init &rArr; oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1f4]"></a>OLED_operate_gram</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, oled.o(i.OLED_operate_gram))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = OLED_operate_gram
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[14b]"></a>OLED_printf</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, oled.o(i.OLED_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = OLED_printf &rArr; OLED_show_string &rArr; OLED_refresh_gram &rArr; OLED_set_pos &rArr; oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_show_string
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[14e]"></a>OLED_refresh_gram</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, oled.o(i.OLED_refresh_gram))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_refresh_gram &rArr; OLED_set_pos &rArr; oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_byte
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_set_pos
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_show_string
</UL>

<P><STRONG><a name="[14f]"></a>OLED_set_pos</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, oled.o(i.OLED_set_pos))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_set_pos &rArr; oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_refresh_gram
</UL>

<P><STRONG><a name="[150]"></a>OLED_show_char</STRONG> (Thumb, 116 bytes, Stack size 36 bytes, oled.o(i.OLED_show_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_show_char &rArr; OLED_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_show_string
</UL>

<P><STRONG><a name="[14d]"></a>OLED_show_string</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, oled.o(i.OLED_show_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_show_string &rArr; OLED_refresh_gram &rArr; OLED_set_pos &rArr; oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_show_char
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_refresh_gram
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_printf
</UL>

<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[1f6]"></a>SERVO_Clamp</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, servo.o(i.SERVO_Clamp))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[152]"></a>SERVO_Init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, servo.o(i.SERVO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SERVO_Init &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1f5]"></a>SERVO_Release</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, servo.o(i.SERVO_Release))
<BR><BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[153]"></a>ST_IICreadBytes</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, st_i2c.o(i.ST_IICreadBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_UpdateByte
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ReadMulti
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdDWord
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>

<P><STRONG><a name="[154]"></a>ST_IICwriteByte</STRONG> (Thumb, 12 bytes, Stack size 16 bytes, st_i2c.o(i.ST_IICwriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_UpdateByte
</UL>

<P><STRONG><a name="[155]"></a>ST_IICwriteBytes</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, st_i2c.o(i.ST_IICwriteBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteByte
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WriteMulti
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrWord
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrDWord
</UL>

<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[157]"></a>SystemClock_Config</STRONG> (Thumb, 144 bytes, Stack size 128 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 20 bytes, system_stm32h7xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(.text)
</UL>
<P><STRONG><a name="[27]"></a>TIM2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TIM2_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[f6]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[ff]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
</UL>

<P><STRONG><a name="[f9]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[10c]"></a>TIM_OC2_SetConfig</STRONG> (Thumb, 126 bytes, Stack size 20 bytes, stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[125]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 200 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[127]"></a>UART_CheckIdleState</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, stm32h7xx_hal_uart.o(i.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[126]"></a>UART_SetConfig</STRONG> (Thumb, 914 bytes, Stack size 56 bytes, stm32h7xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPLL3ClockFreq
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPLL2ClockFreq
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetD3PCLK1Freq
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[12b]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
</UL>

<P><STRONG><a name="[129]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
</UL>

<P><STRONG><a name="[31]"></a>USART2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h7xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = USART2_IRQHandler &rArr; uart2_receiver_handle &rArr; HAL_UART_Receive &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_receiver_handle
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART3_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32h7xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = USART3_IRQHandler &rArr; atgm336h_usart3_irq_handler &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>USART6_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32h7xx_it.o(i.USART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = USART6_IRQHandler &rArr; v831_usart6_irq_handler &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h723xx.o(RESET)
</UL>
<P><STRONG><a name="[15f]"></a>VL53L0X_CheckAndLoadInterruptSettings</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = VL53L0X_CheckAndLoadInterruptSettings &rArr; VL53L0X_GetInterruptThresholds &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_load_tuning_settings
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterruptThresholds
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StartMeasurement
</UL>

<P><STRONG><a name="[163]"></a>VL53L0X_ClearInterruptMask</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_ClearInterruptMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = VL53L0X_ClearInterruptMask &rArr; VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_single_ref_calibration
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetGpioConfig
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleRangingMeasurement
</UL>

<P><STRONG><a name="[165]"></a>VL53L0X_DataInit</STRONG> (Thumb, 388 bytes, Stack size 96 bytes, vl53l0x_api.o(i.VL53L0X_DataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = VL53L0X_DataInit &rArr; VL53L0X_GetDeviceParameters &rArr; VL53L0X_GetMeasurementTimingBudgetMicroSeconds &rArr; VL53L0X_get_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetLimitCheckValue
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetLimitCheckEnable
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
</UL>

<P><STRONG><a name="[166]"></a>VL53L0X_GetDeviceParameters</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_GetDeviceParameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = VL53L0X_GetDeviceParameters &rArr; VL53L0X_GetMeasurementTimingBudgetMicroSeconds &rArr; VL53L0X_get_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetXTalkCompensationRateMegaCps
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetWrapAroundCheckEnable
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetOffsetCalibrationDataMicroMeter
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetMeasurementTimingBudgetMicroSeconds
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetLimitCheckValue
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetLimitCheckEnable
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterMeasurementPeriodMilliSeconds
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
</UL>

<P><STRONG><a name="[171]"></a>VL53L0X_GetFractionEnable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, vl53l0x_api.o(i.VL53L0X_GetFractionEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = VL53L0X_GetFractionEnable &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[16a]"></a>VL53L0X_GetInterMeasurementPeriodMilliSeconds</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = VL53L0X_GetInterMeasurementPeriodMilliSeconds &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdDWord
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[174]"></a>VL53L0X_GetInterruptMaskStatus</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_GetInterruptMaskStatus &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetMeasurementDataReady
</UL>

<P><STRONG><a name="[160]"></a>VL53L0X_GetInterruptThresholds</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = VL53L0X_GetInterruptThresholds &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_CheckAndLoadInterruptSettings
</UL>

<P><STRONG><a name="[16e]"></a>VL53L0X_GetLimitCheckEnable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[16d]"></a>VL53L0X_GetLimitCheckValue</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[175]"></a>VL53L0X_GetMeasurementDataReady</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = VL53L0X_GetMeasurementDataReady &rArr; VL53L0X_GetInterruptMaskStatus &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterruptMaskStatus
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_measurement_poll_for_completion
</UL>

<P><STRONG><a name="[170]"></a>VL53L0X_GetMeasurementTimingBudgetMicroSeconds</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VL53L0X_GetMeasurementTimingBudgetMicroSeconds &rArr; VL53L0X_get_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_measurement_timing_budget_micro_seconds
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[16c]"></a>VL53L0X_GetOffsetCalibrationDataMicroMeter</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = VL53L0X_GetOffsetCalibrationDataMicroMeter &rArr; VL53L0X_get_offset_calibration_data_micro_meter &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_offset_calibration_data_micro_meter
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[178]"></a>VL53L0X_GetRangingMeasurementData</STRONG> (Thumb, 320 bytes, Stack size 96 bytes, vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ReadMulti
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetValue
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleRangingMeasurement
</UL>

<P><STRONG><a name="[17b]"></a>VL53L0X_GetSequenceStepEnables</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_GetSequenceStepEnables &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sequence_step_enabled
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_measurement_timing_budget_micro_seconds
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_measurement_timing_budget_micro_seconds
</UL>

<P><STRONG><a name="[17d]"></a>VL53L0X_GetValue</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_GetValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = VL53L0X_GetValue &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleMeasurement
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetRangingMeasurementData
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[17f]"></a>VL53L0X_GetVcselPulsePeriod</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_vcsel_pulse_period
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[16f]"></a>VL53L0X_GetWrapAroundCheckEnable</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_GetWrapAroundCheckEnable &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[1a2]"></a>VL53L0X_GetXTalkCompensationEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable))
<BR><BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_total_xtalk_rate
</UL>

<P><STRONG><a name="[16b]"></a>VL53L0X_GetXTalkCompensationRateMegaCps</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = VL53L0X_GetXTalkCompensationRateMegaCps &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
</UL>

<P><STRONG><a name="[181]"></a>VL53L0X_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = VL53L0X_Init &rArr; VL53L0X_SetPowerMode &rArr; VL53L0X_StaticInit &rArr; VL53L0X_perform_ref_spad_management &rArr; perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetPowerMode
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetInterMeasurementPeriodMilliSeconds
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetDeviceMode
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetDeviceAddress
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[17e]"></a>VL53L0X_PerformSingleMeasurement</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = VL53L0X_PerformSingleMeasurement &rArr; VL53L0X_StartMeasurement &rArr; VL53L0X_CheckAndLoadInterruptSettings &rArr; VL53L0X_GetInterruptThresholds &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_measurement_poll_for_completion
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StartMeasurement
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetValue
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleRangingMeasurement
</UL>

<P><STRONG><a name="[188]"></a>VL53L0X_PerformSingleRangingMeasurement</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ClearInterruptMask
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetDeviceMode
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleMeasurement
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetRangingMeasurementData
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_signal_measurement
</UL>

<P><STRONG><a name="[189]"></a>VL53L0X_PollingDelay</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_PollingDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = VL53L0X_PollingDelay &rArr; delay_ms &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_measurement_poll_for_completion
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
</UL>

<P><STRONG><a name="[164]"></a>VL53L0X_RdByte</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_RdByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ref_calibration_io
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_vcsel_pulse_period
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StartMeasurement
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetSequenceStepEnable
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetWrapAroundCheckEnable
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetMeasurementDataReady
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterruptMaskStatus
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetFractionEnable
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ClearInterruptMask
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_device_read_strobe
</UL>

<P><STRONG><a name="[173]"></a>VL53L0X_RdDWord</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_RdDWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = VL53L0X_RdDWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterMeasurementPeriodMilliSeconds
</UL>

<P><STRONG><a name="[172]"></a>VL53L0X_RdWord</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_RdWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_signal_measurement
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_offset_calibration_data_micro_meter
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetXTalkCompensationRateMegaCps
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetLimitCheckValue
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterruptThresholds
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetInterMeasurementPeriodMilliSeconds
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetInterMeasurementPeriodMilliSeconds
</UL>

<P><STRONG><a name="[179]"></a>VL53L0X_ReadMulti</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_ReadMulti))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = VL53L0X_ReadMulti &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ref_spad_map
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetRangingMeasurementData
</UL>

<P><STRONG><a name="[182]"></a>VL53L0X_SetDeviceAddress</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_SetDeviceAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_SetDeviceAddress &rArr; VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
</UL>

<P><STRONG><a name="[184]"></a>VL53L0X_SetDeviceMode</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_SetDeviceMode))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleRangingMeasurement
</UL>

<P><STRONG><a name="[18b]"></a>VL53L0X_SetGpioConfig</STRONG> (Thumb, 312 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_SetGpioConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = VL53L0X_SetGpioConfig &rArr; VL53L0X_ClearInterruptMask &rArr; VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ClearInterruptMask
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_UpdateByte
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[185]"></a>VL53L0X_SetInterMeasurementPeriodMilliSeconds</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = VL53L0X_SetInterMeasurementPeriodMilliSeconds &rArr; VL53L0X_WrDWord &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrDWord
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
</UL>

<P><STRONG><a name="[168]"></a>VL53L0X_SetLimitCheckEnable</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = VL53L0X_SetLimitCheckEnable &rArr; VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrWord
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_UpdateByte
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
</UL>

<P><STRONG><a name="[169]"></a>VL53L0X_SetLimitCheckValue</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_SetLimitCheckValue &rArr; VL53L0X_WrWord &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrWord
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
</UL>

<P><STRONG><a name="[18f]"></a>VL53L0X_SetMeasurementTimingBudgetMicroSeconds</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = VL53L0X_SetMeasurementTimingBudgetMicroSeconds &rArr; VL53L0X_set_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_measurement_timing_budget_micro_seconds
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetSequenceStepEnable
</UL>

<P><STRONG><a name="[183]"></a>VL53L0X_SetPowerMode</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, vl53l0x_api.o(i.VL53L0X_SetPowerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 736<LI>Call Chain = VL53L0X_SetPowerMode &rArr; VL53L0X_StaticInit &rArr; VL53L0X_perform_ref_spad_management &rArr; perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
</UL>

<P><STRONG><a name="[192]"></a>VL53L0X_SetSequenceStepEnable</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = VL53L0X_SetSequenceStepEnable &rArr; VL53L0X_SetMeasurementTimingBudgetMicroSeconds &rArr; VL53L0X_set_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetMeasurementTimingBudgetMicroSeconds
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[186]"></a>VL53L0X_StartMeasurement</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, vl53l0x_api.o(i.VL53L0X_StartMeasurement))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = VL53L0X_StartMeasurement &rArr; VL53L0X_CheckAndLoadInterruptSettings &rArr; VL53L0X_GetInterruptThresholds &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_CheckAndLoadInterruptSettings
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleMeasurement
</UL>

<P><STRONG><a name="[191]"></a>VL53L0X_StaticInit</STRONG> (Thumb, 366 bytes, Stack size 104 bytes, vl53l0x_api.o(i.VL53L0X_StaticInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 720<LI>Call Chain = VL53L0X_StaticInit &rArr; VL53L0X_perform_ref_spad_management &rArr; perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_reference_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_load_tuning_settings
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetSequenceStepEnable
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetGpioConfig
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetVcselPulsePeriod
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetFractionEnable
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetDeviceParameters
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetPowerMode
</UL>

<P><STRONG><a name="[18c]"></a>VL53L0X_UpdateByte</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_UpdateByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteByte
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICreadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ref_calibration_io
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetLimitCheckEnable
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetGpioConfig
</UL>

<P><STRONG><a name="[162]"></a>VL53L0X_WrByte</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_WrByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_signal_measurement
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ref_calibration_io
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_vhv_calibration
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_single_ref_calibration
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_phase_calibration
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_reference_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_calibration
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StartMeasurement
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetSequenceStepEnable
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetGpioConfig
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ClearInterruptMask
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_CheckAndLoadInterruptSettings
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetPowerMode
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetDeviceAddress
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_DataInit
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_device_read_strobe
</UL>

<P><STRONG><a name="[18d]"></a>VL53L0X_WrDWord</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_WrDWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = VL53L0X_WrDWord &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetInterMeasurementPeriodMilliSeconds
</UL>

<P><STRONG><a name="[18e]"></a>VL53L0X_WrWord</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_WrWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = VL53L0X_WrWord &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetLimitCheckValue
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetLimitCheckEnable
</UL>

<P><STRONG><a name="[197]"></a>VL53L0X_WriteMulti</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, vl53l0x_i2c_dev.o(i.VL53L0X_WriteMulti))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = VL53L0X_WriteMulti &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ST_IICwriteBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ref_spad_map
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_load_tuning_settings
</UL>

<P><STRONG><a name="[198]"></a>VL53L0X_calc_dmax</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, vl53l0x_api_core.o(i.VL53L0X_calc_dmax))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = VL53L0X_calc_dmax
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_isqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
</UL>

<P><STRONG><a name="[19e]"></a>VL53L0X_calc_macro_period_ps</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps))
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_timeout_us
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_timeout_mclks
</UL>

<P><STRONG><a name="[19a]"></a>VL53L0X_calc_sigma_estimate</STRONG> (Thumb, 556 bytes, Stack size 80 bytes, vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = VL53L0X_calc_sigma_estimate &rArr; VL53L0X_get_total_signal_rate &rArr; VL53L0X_get_total_xtalk_rate
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_total_signal_rate
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_isqrt
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_total_xtalk_rate
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_timeout_mclks
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_dmax
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_pal_range_status
</UL>

<P><STRONG><a name="[19d]"></a>VL53L0X_calc_timeout_mclks</STRONG> (Thumb, 34 bytes, Stack size 4 bytes, vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = VL53L0X_calc_timeout_mclks
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_macro_period_ps
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
</UL>

<P><STRONG><a name="[19f]"></a>VL53L0X_calc_timeout_us</STRONG> (Thumb, 34 bytes, Stack size 4 bytes, vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = VL53L0X_calc_timeout_us
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_macro_period_ps
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
</UL>

<P><STRONG><a name="[1d5]"></a>VL53L0X_decode_timeout</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, vl53l0x_api_core.o(i.VL53L0X_decode_timeout))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
</UL>

<P><STRONG><a name="[1a3]"></a>VL53L0X_decode_vcsel_period</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period))
<BR><BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_vcsel_pulse_period
</UL>

<P><STRONG><a name="[1a0]"></a>VL53L0X_device_read_strobe</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, vl53l0x_api_core.o(i.VL53L0X_device_read_strobe))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = VL53L0X_device_read_strobe &rArr; VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_info_from_device
</UL>

<P><STRONG><a name="[1e8]"></a>VL53L0X_encode_timeout</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, vl53l0x_api_core.o(i.VL53L0X_encode_timeout))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
</UL>

<P><STRONG><a name="[193]"></a>VL53L0X_get_info_from_device</STRONG> (Thumb, 1322 bytes, Stack size 120 bytes, vl53l0x_api_core.o(i.VL53L0X_get_info_from_device))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = VL53L0X_get_info_from_device &rArr; VL53L0X_device_read_strobe &rArr; VL53L0X_WrByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdDWord
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PollingDelay
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_device_read_strobe
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[176]"></a>VL53L0X_get_measurement_timing_budget_micro_seconds</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = VL53L0X_get_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetMeasurementTimingBudgetMicroSeconds
</UL>

<P><STRONG><a name="[177]"></a>VL53L0X_get_offset_calibration_data_micro_meter</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = VL53L0X_get_offset_calibration_data_micro_meter &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetOffsetCalibrationDataMicroMeter
</UL>

<P><STRONG><a name="[17a]"></a>VL53L0X_get_pal_range_status</STRONG> (Thumb, 482 bytes, Stack size 96 bytes, vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetLimitCheckValue
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetLimitCheckEnable
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetRangingMeasurementData
</UL>

<P><STRONG><a name="[19b]"></a>VL53L0X_get_total_signal_rate</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = VL53L0X_get_total_signal_rate &rArr; VL53L0X_get_total_xtalk_rate
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_total_xtalk_rate
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
</UL>

<P><STRONG><a name="[19c]"></a>VL53L0X_get_total_xtalk_rate</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = VL53L0X_get_total_xtalk_rate
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetXTalkCompensationEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_total_signal_rate
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
</UL>

<P><STRONG><a name="[180]"></a>VL53L0X_get_vcsel_pulse_period</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_decode_vcsel_period
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetVcselPulsePeriod
</UL>

<P><STRONG><a name="[199]"></a>VL53L0X_isqrt</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, vl53l0x_api_core.o(i.VL53L0X_isqrt))
<BR><BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_sigma_estimate
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_dmax
</UL>

<P><STRONG><a name="[161]"></a>VL53L0X_load_tuning_settings</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = VL53L0X_load_tuning_settings &rArr; VL53L0X_WriteMulti &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WriteMulti
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_CheckAndLoadInterruptSettings
</UL>

<P><STRONG><a name="[187]"></a>VL53L0X_measurement_poll_for_completion</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = VL53L0X_measurement_poll_for_completion &rArr; VL53L0X_GetMeasurementDataReady &rArr; VL53L0X_GetInterruptMaskStatus &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetMeasurementDataReady
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PollingDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_single_ref_calibration
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleMeasurement
</UL>

<P><STRONG><a name="[1a4]"></a>VL53L0X_perform_phase_calibration</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = VL53L0X_perform_phase_calibration &rArr; VL53L0X_ref_calibration_io &rArr; VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ref_calibration_io
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_single_ref_calibration
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_calibration
</UL>

<P><STRONG><a name="[1a7]"></a>VL53L0X_perform_ref_calibration</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = VL53L0X_perform_ref_calibration &rArr; VL53L0X_perform_vhv_calibration &rArr; VL53L0X_ref_calibration_io &rArr; VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_vhv_calibration
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_phase_calibration
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[194]"></a>VL53L0X_perform_ref_spad_management</STRONG> (Thumb, 540 bytes, Stack size 144 bytes, vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = VL53L0X_perform_ref_spad_management &rArr; perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ref_spad_map
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_signal_measurement
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_aperture
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_good_spad
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_spad_bit
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_calibration
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[1a5]"></a>VL53L0X_perform_single_ref_calibration</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = VL53L0X_perform_single_ref_calibration &rArr; VL53L0X_measurement_poll_for_completion &rArr; VL53L0X_GetMeasurementDataReady &rArr; VL53L0X_GetInterruptMaskStatus &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_measurement_poll_for_completion
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ClearInterruptMask
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_vhv_calibration
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_phase_calibration
</UL>

<P><STRONG><a name="[1a8]"></a>VL53L0X_perform_vhv_calibration</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = VL53L0X_perform_vhv_calibration &rArr; VL53L0X_ref_calibration_io &rArr; VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ref_calibration_io
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_single_ref_calibration
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_calibration
</UL>

<P><STRONG><a name="[1a6]"></a>VL53L0X_ref_calibration_io</STRONG> (Thumb, 178 bytes, Stack size 56 bytes, vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = VL53L0X_ref_calibration_io &rArr; VL53L0X_UpdateByte &rArr; ST_IICwriteByte &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_UpdateByte
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_vhv_calibration
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_phase_calibration
</UL>

<P><STRONG><a name="[190]"></a>VL53L0X_set_measurement_timing_budget_micro_seconds</STRONG> (Thumb, 196 bytes, Stack size 40 bytes, vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = VL53L0X_set_measurement_timing_budget_micro_seconds &rArr; get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_step_timeout
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sequence_step_timeout
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_SetMeasurementTimingBudgetMicroSeconds
</UL>

<P><STRONG><a name="[195]"></a>VL53L0X_set_reference_spads</STRONG> (Thumb, 166 bytes, Stack size 56 bytes, vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = VL53L0X_set_reference_spads &rArr; enable_ref_spads &rArr; set_ref_spad_map &rArr; VL53L0X_WriteMulti &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_aperture
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[1b0]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[20e]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1c0]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_usart6_irq_handler
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_usart3_irq_handler
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printV831Data
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printGpsBuffer
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_json_msg
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[20f]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[210]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1b2]"></a>__0vsprintf</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[211]"></a>__1vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[212]"></a>__2vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[213]"></a>__c89vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[14c]"></a>vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_printf
</UL>

<P><STRONG><a name="[af]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[214]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[1b3]"></a>__hardfp_atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseGpsBuffer
</UL>

<P><STRONG><a name="[1b4]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[215]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[216]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[217]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[1b5]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[c5]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[1be]"></a>atgm336h_init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, atgm336h.o(i.atgm336h_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = atgm336h_init &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clrStruct
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[15d]"></a>atgm336h_usart3_irq_handler</STRONG> (Thumb, 480 bytes, Stack size 32 bytes, atgm336h.o(i.atgm336h_usart3_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = atgm336h_usart3_irq_handler &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[1c2]"></a>clearV831Data</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, v831.o(i.clearV831Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clearV831Data
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
</UL>

<P><STRONG><a name="[1bf]"></a>clrStruct</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, atgm336h.o(i.clrStruct))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clrStruct
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
</UL>

<P><STRONG><a name="[1c3]"></a>delay_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, delay.o(i.delay_init))
<BR><BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[18a]"></a>delay_ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_ms &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PollingDelay
</UL>

<P><STRONG><a name="[1c7]"></a>delay_us</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_bit
</UL>

<P><STRONG><a name="[1c5]"></a>dht11_check</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, dht11.o(i.dht11_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dht11_check &rArr; dht11_io_in &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_in
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_data
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_init
</UL>

<P><STRONG><a name="[1c9]"></a>dht11_init</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, dht11.o(i.dht11_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = dht11_init &rArr; dht11_check &rArr; dht11_io_in &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1c6]"></a>dht11_io_in</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, dht11.o(i.dht11_io_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = dht11_io_in &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_check
</UL>

<P><STRONG><a name="[1cb]"></a>dht11_io_out</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, dht11.o(i.dht11_io_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = dht11_io_out &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
</UL>

<P><STRONG><a name="[1cc]"></a>dht11_read_bit</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, dht11.o(i.dht11_read_bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = dht11_read_bit &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_byte
</UL>

<P><STRONG><a name="[1cd]"></a>dht11_read_byte</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dht11.o(i.dht11_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dht11_read_byte &rArr; dht11_read_bit &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_data
</UL>

<P><STRONG><a name="[1ce]"></a>dht11_read_data</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, dht11.o(i.dht11_read_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = dht11_read_data &rArr; dht11_check &rArr; dht11_io_in &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_rst
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[1ca]"></a>dht11_rst</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dht11.o(i.dht11_rst))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = dht11_rst &rArr; dht11_io_out &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_io_out
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_data
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_init
</UL>

<P><STRONG><a name="[1a9]"></a>enable_ref_spads</STRONG> (Thumb, 148 bytes, Stack size 64 bytes, vl53l0x_api_calibration.o(i.enable_ref_spads))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = enable_ref_spads &rArr; set_ref_spad_map &rArr; VL53L0X_WriteMulti &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_ref_spad_map
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_aperture
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ref_spad_map
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_good_spad
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_spad_bit
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_reference_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[1ad]"></a>enable_spad_bit</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, vl53l0x_api_calibration.o(i.enable_spad_bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = enable_spad_bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[1d0]"></a>esp8266_receive_msg</STRONG> (Thumb, 112 bytes, Stack size 144 bytes, esp8266.o(i.esp8266_receive_msg))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = esp8266_receive_msg &rArr; parse_json_msg &rArr; JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_receiver_clear
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_json_msg
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[a0]"></a>fputc</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, uart_printf.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[1ac]"></a>get_next_good_spad</STRONG> (Thumb, 96 bytes, Stack size 28 bytes, vl53l0x_api_calibration.o(i.get_next_good_spad))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = get_next_good_spad
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[1cf]"></a>get_ref_spad_map</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, vl53l0x_api_calibration.o(i.get_ref_spad_map))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = get_ref_spad_map &rArr; VL53L0X_ReadMulti &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_ReadMulti
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
</UL>

<P><STRONG><a name="[196]"></a>get_sequence_step_timeout</STRONG> (Thumb, 244 bytes, Stack size 64 bytes, vl53l0x_api_core.o(i.get_sequence_step_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = get_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetVcselPulsePeriod
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdByte
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_decode_timeout
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_timeout_us
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_measurement_timing_budget_micro_seconds
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_get_measurement_timing_budget_micro_seconds
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_StaticInit
</UL>

<P><STRONG><a name="[1ab]"></a>is_aperture</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, vl53l0x_api_calibration.o(i.is_aperture))
<BR><BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_reference_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[94]"></a>main</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = main &rArr; user_main_program &rArr; esp8266_receive_msg &rArr; parse_json_msg &rArr; JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Enable
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Disable
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_ConfigRegion
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[14a]"></a>oled_write_byte</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, oled.o(i.oled_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = oled_write_byte &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_set_pos
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_refresh_gram
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_init
</UL>

<P><STRONG><a name="[1e0]"></a>parseGpsBuffer</STRONG> (Thumb, 402 bytes, Stack size 168 bytes, atgm336h.o(i.parseGpsBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = parseGpsBuffer &rArr; __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[1e3]"></a>parseV831Buffer</STRONG> (Thumb, 538 bytes, Stack size 80 bytes, v831.o(i.parseV831Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = parseV831Buffer &rArr; __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[1d3]"></a>parse_json_msg</STRONG> (Thumb, 108 bytes, Stack size 56 bytes, esp8266.o(i.parse_json_msg))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = parse_json_msg &rArr; JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Validate
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_SearchT
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[1aa]"></a>perform_ref_signal_measurement</STRONG> (Thumb, 100 bytes, Stack size 48 bytes, vl53l0x_api_calibration.o(i.perform_ref_signal_measurement))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_PerformSingleRangingMeasurement
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[1e5]"></a>printGpsBuffer</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, atgm336h.o(i.printGpsBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = printGpsBuffer &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[1e6]"></a>printV831Data</STRONG> (Thumb, 94 bytes, Stack size 40 bytes, v831.o(i.printV831Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = printV831Data &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[17c]"></a>sequence_step_enabled</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, vl53l0x_api.o(i.sequence_step_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sequence_step_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
</UL>

<P><STRONG><a name="[1e7]"></a>set_led</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, led.o(i.set_led))
<BR><BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_main_program
</UL>

<P><STRONG><a name="[1ae]"></a>set_ref_spad_map</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, vl53l0x_api_calibration.o(i.set_ref_spad_map))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = set_ref_spad_map &rArr; VL53L0X_WriteMulti &rArr; ST_IICwriteBytes &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WriteMulti
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_ref_spads
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_perform_ref_spad_management
</UL>

<P><STRONG><a name="[1af]"></a>set_sequence_step_timeout</STRONG> (Thumb, 260 bytes, Stack size 40 bytes, vl53l0x_api_core.o(i.set_sequence_step_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = set_sequence_step_timeout &rArr; VL53L0X_GetVcselPulsePeriod &rArr; VL53L0X_get_vcsel_pulse_period &rArr; VL53L0X_RdByte &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetVcselPulsePeriod
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetSequenceStepEnables
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrWord
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_WrByte
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_RdWord
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_encode_timeout
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_decode_timeout
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_calc_timeout_mclks
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_set_measurement_timing_budget_micro_seconds
</UL>

<P><STRONG><a name="[1f0]"></a>time_slot_start</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, time_handle.o(i.time_slot_start))
<BR><BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[1d4]"></a>uart2_receiver_clear</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, esp8266.o(i.uart2_receiver_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = uart2_receiver_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
</UL>

<P><STRONG><a name="[15c]"></a>uart2_receiver_handle</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, esp8266.o(i.uart2_receiver_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = uart2_receiver_handle &rArr; HAL_UART_Receive &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[1d9]"></a>user_init_program</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, main_program.o(i.user_init_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 752<LI>Call Chain = user_init_program &rArr; VL53L0X_Init &rArr; VL53L0X_SetPowerMode &rArr; VL53L0X_StaticInit &rArr; VL53L0X_perform_ref_spad_management &rArr; perform_ref_signal_measurement &rArr; VL53L0X_PerformSingleRangingMeasurement &rArr; VL53L0X_GetRangingMeasurementData &rArr; VL53L0X_get_pal_range_status &rArr; VL53L0X_GetLimitCheckValue &rArr; VL53L0X_RdWord &rArr; ST_IICreadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;v831_init
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time_slot_start
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atgm336h_init
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SERVO_Release
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SERVO_Init
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_operate_gram
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Control
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_printf
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1da]"></a>user_main_program</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, main_program.o(i.user_main_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 808<LI>Call Chain = user_main_program &rArr; esp8266_receive_msg &rArr; parse_json_msg &rArr; JSON_SearchT &rArr; JSON_SearchConst &rArr; multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printV831Data
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printGpsBuffer
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseV831Buffer
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseGpsBuffer
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VL53L0X_GetValue
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SERVO_Clamp
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Control
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Update
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_GetDistanceMM
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_led
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_printf
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;esp8266_receive_msg
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dht11_read_data
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f2]"></a>v831_init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, v831.o(i.v831_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = v831_init &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clearV831Data
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_init_program
</UL>

<P><STRONG><a name="[15e]"></a>v831_usart6_irq_handler</STRONG> (Thumb, 372 bytes, Stack size 24 bytes, v831.o(i.v831_usart6_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = v831_usart6_irq_handler &rArr; HAL_UART_Receive_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[ea]"></a>__NVIC_SetPriority</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[ee]"></a>RCCEx_PLL2_Config</STRONG> (Thumb, 284 bytes, Stack size 32 bytes, stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RCCEx_PLL2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[ef]"></a>RCCEx_PLL3_Config</STRONG> (Thumb, 284 bytes, Stack size 32 bytes, stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[ce]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 168 bytes, Stack size 0 bytes, stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[cf]"></a>DMA_CalcDMAMUXChannelBaseAndMask</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_CalcDMAMUXChannelBaseAndMask
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[d0]"></a>DMA_CalcDMAMUXRequestGenBaseAndMask</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CalcDMAMUXRequestGenBaseAndMask
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[cd]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[d2]"></a>DMA_SetConfig</STRONG> (Thumb, 518 bytes, Stack size 36 bytes, stm32h7xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[12e]"></a>I2C_Flush_TXDR</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsErrorOccurred
</UL>

<P><STRONG><a name="[12d]"></a>I2C_IsErrorOccurred</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Flush_TXDR
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[dc]"></a>I2C_RequestMemoryRead</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = I2C_RequestMemoryRead &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_TransferConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[de]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXISFlagUntilTimeout
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_TransferConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[d8]"></a>I2C_TransferConfig</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32h7xx_hal_i2c.o(i.I2C_TransferConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_TransferConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[d7]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_WaitOnFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsErrorOccurred
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[da]"></a>I2C_WaitOnSTOPFlagUntilTimeout</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = I2C_WaitOnSTOPFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsErrorOccurred
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[d9]"></a>I2C_WaitOnTXISFlagUntilTimeout</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = I2C_WaitOnTXISFlagUntilTimeout &rArr; I2C_IsErrorOccurred
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsErrorOccurred
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[fc]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[10b]"></a>TIM_OC1_SetConfig</STRONG> (Thumb, 140 bytes, Stack size 36 bytes, stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[10d]"></a>TIM_OC3_SetConfig</STRONG> (Thumb, 126 bytes, Stack size 20 bytes, stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[10e]"></a>TIM_OC4_SetConfig</STRONG> (Thumb, 96 bytes, Stack size 20 bytes, stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[10f]"></a>TIM_OC5_SetConfig</STRONG> (Thumb, 90 bytes, Stack size 20 bytes, stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC5_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[110]"></a>TIM_OC6_SetConfig</STRONG> (Thumb, 90 bytes, Stack size 20 bytes, stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC6_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[fa]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[fb]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[9c]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[9f]"></a>UART_DMAError</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32h7xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART_DMAError &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[9d]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAReceiveCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[9e]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMARxHalfCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[11a]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32h7xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[119]"></a>UART_EndTxTransfer</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32h7xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[115]"></a>UARTEx_SetNbDataToProcess</STRONG> (Thumb, 62 bytes, Stack size 12 bytes, stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
</UL>

<P><STRONG><a name="[1bb]"></a>arraySearch</STRONG> (Thumb, 156 bytes, Stack size 64 bytes, core_json.o(i.arraySearch))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = arraySearch &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextValue
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiSearch
</UL>

<P><STRONG><a name="[131]"></a>getType</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, core_json.o(i.getType))
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_SearchConst
</UL>

<P><STRONG><a name="[1ec]"></a>hexToInt</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, core_json.o(i.hexToInt))
<BR><BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipOneHexEscape
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipDigits
</UL>

<P><STRONG><a name="[130]"></a>multiSearch</STRONG> (Thumb, 252 bytes, Stack size 80 bytes, core_json.o(i.multiSearch))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = multiSearch &rArr; objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipDigits
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;objectSearch
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arraySearch
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_SearchConst
</UL>

<P><STRONG><a name="[1dd]"></a>nextKeyValuePair</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, core_json.o(i.nextKeyValuePair))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipString
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextValue
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;objectSearch
</UL>

<P><STRONG><a name="[1bc]"></a>nextValue</STRONG> (Thumb, 100 bytes, Stack size 40 bytes, core_json.o(i.nextValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipCollection
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextKeyValuePair
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arraySearch
</UL>

<P><STRONG><a name="[1dc]"></a>objectSearch</STRONG> (Thumb, 178 bytes, Stack size 88 bytes, core_json.o(i.objectSearch))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = objectSearch &rArr; nextKeyValuePair &rArr; nextValue &rArr; skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strnEq
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextKeyValuePair
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiSearch
</UL>

<P><STRONG><a name="[135]"></a>skipAnyScalar</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, core_json.o(i.skipAnyScalar))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipString
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipNumber
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipLiteral
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Validate
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipScalars
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipObjectScalars
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextValue
</UL>

<P><STRONG><a name="[136]"></a>skipCollection</STRONG> (Thumb, 168 bytes, Stack size 80 bytes, core_json.o(i.skipCollection))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = skipCollection &rArr; skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipScalars
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Validate
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextValue
</UL>

<P><STRONG><a name="[1db]"></a>skipDigits</STRONG> (Thumb, 116 bytes, Stack size 40 bytes, core_json.o(i.skipDigits))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = skipDigits
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hexToInt
</UL>
<BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipNumber
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multiSearch
</UL>

<P><STRONG><a name="[1ed]"></a>skipEscape</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, core_json.o(i.skipEscape))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipOneHexEscape
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipString
</UL>

<P><STRONG><a name="[1e9]"></a>skipLiteral</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, core_json.o(i.skipLiteral))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = skipLiteral &rArr; strnEq
</UL>
<BR>[Calls]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strnEq
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>

<P><STRONG><a name="[1ea]"></a>skipNumber</STRONG> (Thumb, 182 bytes, Stack size 40 bytes, core_json.o(i.skipNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = skipNumber &rArr; skipDigits
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipDigits
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>

<P><STRONG><a name="[1ef]"></a>skipObjectScalars</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, core_json.o(i.skipObjectScalars))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipString
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipScalars
</UL>

<P><STRONG><a name="[1ee]"></a>skipOneHexEscape</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, core_json.o(i.skipOneHexEscape))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hexToInt
</UL>
<BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipEscape
</UL>

<P><STRONG><a name="[1eb]"></a>skipScalars</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, core_json.o(i.skipScalars))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = skipScalars &rArr; skipObjectScalars &rArr; skipAnyScalar &rArr; skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipObjectScalars
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipCollection
</UL>

<P><STRONG><a name="[134]"></a>skipSpace</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, core_json.o(i.skipSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = skipSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Validate
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpaceAndComma
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipScalars
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipObjectScalars
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;objectSearch
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextKeyValuePair
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arraySearch
</UL>

<P><STRONG><a name="[1bd]"></a>skipSpaceAndComma</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, core_json.o(i.skipSpaceAndComma))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = skipSpaceAndComma &rArr; skipSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipScalars
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipObjectScalars
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipCollection
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;objectSearch
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arraySearch
</UL>

<P><STRONG><a name="[1de]"></a>skipString</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, core_json.o(i.skipString))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = skipString &rArr; skipEscape &rArr; skipOneHexEscape
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipEscape
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipObjectScalars
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipAnyScalar
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nextKeyValuePair
</UL>

<P><STRONG><a name="[1df]"></a>strnEq</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, core_json.o(i.strnEq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strnEq
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skipLiteral
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;objectSearch
</UL>

<P><STRONG><a name="[1b6]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1b1]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[1b9]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1b8]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a1]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsprintf)
</UL>
<P><STRONG><a name="[9a]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[b2]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[c2]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
